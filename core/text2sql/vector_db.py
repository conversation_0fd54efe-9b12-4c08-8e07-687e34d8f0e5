from typing import List

import pandas as pd
from langchain_openai import OpenAIEmbeddings
from pymilvus import MilvusClient
from vanna.base import VannaBase
from vanna.utils import deterministic_uuid

from core.config.vector_config import embedding_model_name, embedding_model_api_base
from core.utlis import extract_table_names
from core.config.logging import logger

class MilvusVectorDB(VannaBase):

    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)
        if config is None:
            config = {}

        self.embedding_function = OpenAIEmbeddings(model=embedding_model_name,
                                                   openai_api_base=embedding_model_api_base,
                                                   openai_api_key="...")

        self.n_results_sql = config.get("n_results_sql", config.get("n_results", 10))
        self.n_results_documentation = config.get("n_results_documentation", config.get("n_results", 10))
        self.n_results_ddl = config.get("n_results_ddl", config.get("n_results", 10))

        self.milvus_client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="Text2Sql")

        # self.documentation_collection = self.milvus_client.load_collection(collection_name="documentation")
        # self.ddl_collection = self.milvus_client.load_collection(collection_name="ddl")
        # self.sql_collection = self.milvus_client.load_collection(collection_name="sql")

    def add_ddl(self, ddl: str, **kwargs) -> str:
        id = deterministic_uuid(ddl) + "-ddl"
        self.milvus_client.insert(
            collection_name="ddl",
            data=[
                {
                    "ids": id,
                    "documents": ddl,
                    "documents_summary": kwargs.get("documents_summary"),
                    "table_name": kwargs.get('table_name') if kwargs.get('table_name') else ",".join(extract_table_names(ddl)),
                    "embeddings": self.generate_embedding(kwargs.get("documents_summary")),
                }
            ]
        )
        return id

    def add_documentation(self, documentation: str, **kwargs) -> str:
        id = deterministic_uuid(documentation) + "-doc"
        self.milvus_client.insert(
            collection_name="documentation",
            data=[
                {
                    "ids": id,
                    "documents": documentation,
                    "embeddings": self.generate_embedding(documentation),
                }
            ]
        )
        return id

    def add_question_sql(self, question: str, sql: str, **kwargs) -> str:
        id = deterministic_uuid(question) + "-sql"
        self.milvus_client.insert(
            collection_name="sql",
            data=[
                {
                    "ids": id,
                    "question": question,
                    "sql": sql,
                    "embeddings": self.generate_embedding(question),
                    "table_name": ",".join(extract_table_names(sql))
                }
            ])
        return id

    def get_similar_question_sql(self, question: str, **kwargs) -> list:
        _vector = self.generate_embedding(question)
        out = []

        res = self.milvus_client.search(
            collection_name="sql",
            anns_field="embeddings",
            data=[_vector],
            limit=self.n_results_sql,
            params={"metric_type": "L2"},
            output_fields=["ids", "question", "sql"],
            filter=kwargs.get("where") if kwargs.get("where") else ''
        )
        for hits in res:
            for hit in hits:
                out.append({"question": hit["entity"]["question"], "sql": hit["entity"]["sql"]})

        return out

    def get_related_ddl(self, question: str, **kwargs) -> list:
        _vector = self.generate_embedding(question)
        out = []

        res = self.milvus_client.search(
            collection_name="ddl",
            anns_field="embeddings",
            data=[_vector],
            limit=self.n_results_sql,
            params={"metric_type": "L2"},
            output_fields=["ids", "documents", "documents_summary"],
            filter=kwargs.get("where") if kwargs.get("where") else ''
        )
        for hits in res:
            for hit in hits:
                out.append(
                    f'【表描述】\n{hit["entity"]["documents_summary"]}\n【表信息】\n{hit["entity"]["documents"]}\n')

        return out

    def get_related_documentation(self, question: str, **kwargs) -> list:
        _vector = self.generate_embedding(question)
        out = []

        res = self.milvus_client.search(
            collection_name="documentation",
            anns_field="embeddings",
            data=[_vector],
            limit=self.n_results_sql,
            params={"metric_type": "L2"},
            output_fields=["ids", "documents"],
            filter=kwargs.get("where") if kwargs.get("where") else ''
        )
        for hits in res:
            for hit in hits:
                out.append(hit["entity"]["documents"])

        return out

    def get_training_data(self, **kwargs) -> pd.DataFrame:
        df = pd.DataFrame()
        iterator = self.milvus_client.query_iterator(
            collection_name="sql",
            filter="",
            output_fields=["ids", "question", "sql"],
            batch_size=100
        )

        ids, question, sql = [], [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                question.append(item["question"])
                sql.append(item["sql"])

        # Create a DataFrame
        df_sql = pd.DataFrame(
            {
                "id": ids,
                "question": question,
                "content": sql,
            }
        )

        df_sql["training_data_type"] = "sql"

        df = pd.concat([df, df_sql])

        iterator = self.milvus_client.query_iterator(
            collection_name="ddl",
            filter="",
            output_fields=["ids", "documents", "documents_summary"],
            batch_size=100
        )
        ids, documents = [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                documents.append(item["documents_summary"] + item["documents"])

        # Create a DataFrame
        df_ddl = pd.DataFrame(
            {
                "id": ids,
                "question": [None for doc in documents],
                "content": documents,
            }
        )

        df_ddl["training_data_type"] = "ddl"

        df = pd.concat([df, df_ddl])

        iterator = self.milvus_client.query_iterator(
            collection_name="documentation",
            filter="",
            output_fields=["ids", "documents"],
            batch_size=100
        )
        ids, documents = [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                documents.append(item["documents"])

        # Create a DataFrame
        df_doc = pd.DataFrame(
            {
                "id": ids,
                "question": [None for doc in documents],
                "content": documents,
            }
        )

        df_doc["training_data_type"] = "documentation"

        df = pd.concat([df, df_doc])

        return df

    def remove_training_data(self, id: str, **kwargs) -> bool:
        if id.endswith("-sql"):
            self.milvus_client.delete(
                collection_name="sql",
                filter=f"ids == '{id}'"
            )
            return True
        elif id.endswith("-ddl"):
            self.milvus_client.delete(
                collection_name="ddl",
                filter=f"ids == '{id}'"
            )
            return True
        elif id.endswith("-doc"):
            self.milvus_client.delete(
                collection_name="documentation",
                filter=f"ids == '{id}'"
            )
            return True
        else:
            return False

    def system_message(self, message: str) -> any:
        pass

    def user_message(self, message: str) -> any:
        pass

    def assistant_message(self, message: str) -> any:
        pass

    def submit_prompt(self, prompt, **kwargs) -> str:
        pass

    def generate_embedding(self, data: str, **kwargs) -> List[float]:
        return self.embedding_function.embed_query(data)


if __name__ == '__main__':
    vn = MilvusVectorDB()
    # vn.add_question_sql("查询语句", "select * from table")
    # vn.add_question_sql("查询今日数据", "create table table")
    res = vn.get_similar_question_sql("查询实验车间2025-5-1至2025-5-21的单机效率")
    # res = vn.get_related_ddl("查询生产数据")
    # for r in res:
    #     print(r)

    # print(vn.get_training_data())
    # vn.remove_training_data(id="27e97c3c-d96e-5fec-8f4c-4cd9ce6604e8-sql")

    # import pandas as pd
    #
    # df = pd.read_excel("1.xlsx")
    # for index, row in df.iterrows():
    #     vn.add_question_sql(row[0], row[1])
