from core.config.logging import logger


class SQLCorrector:
    """SQL错误纠正模块，用于分析SQL错误并提供修复建议"""

    def __init__(self, vanna_instance):
        self.vn = vanna_instance
        self.error_history = []  # 记录错误历史
        self.max_correction_attempts = 2  # 最大纠错尝试次数

    def correct_sql(self, question: str, sql: str, error: Exception, role: str):
        """
        尝试纠正SQL错误并重新执行

        Args:
            question: 原始自然语言问题
            sql: 生成的有错误的SQL
            error: 执行SQL时捕获的异常

        Returns:
            tuple: (修正后的SQL, 执行结果DataFrame, 异常(如果仍然失败))
        """
        # 记录错误信息
        error_info = {
            "original_sql": sql,
            "error_message": str(error),
            "error_type": type(error).__name__,
            "question": question
        }
        self.error_history.append(error_info)

        # 尝试多次纠错
        for attempt in range(self.max_correction_attempts):
            logger.info(f"尝试修复SQL错误，尝试次数: {attempt + 1}....")
            # 调用LLM修复SQL
            corrected_sql = self._generate_corrected_sql(sql, error, question, attempt + 1)

            if corrected_sql == sql:
                # 如果修复后的SQL与原SQL相同，说明模型无法修复
                return sql, None, Exception(f"修复后的SQL与原SQL相同，模型无法修复: {str(error)}")

            try:
                # 权限校验模块
                # if not check_db_permission(sql, role):
                #     logger.warning(f"用户角色{role}, sql: {sql}, \n 暂无数据库访问权限！请联系管理员后重试！")
                #     return None, None, "暂无数据访问权限！请联系管理员后重试！"
                # 尝试执行修复后的SQL
                df = self.vn.run_sql(corrected_sql)

                # 如果成功，添加到训练数据
                # if len(df) > 0:
                #     self.vn.add_question_sql(question=question, sql=corrected_sql)

                # 记录成功修复的信息
                self.error_history[-1]["corrected_sql"] = corrected_sql
                self.error_history[-1]["success"] = True
                self.error_history[-1]["attempts"] = attempt + 1

                return corrected_sql, df, None

            except Exception as new_error:
                # 记录此次尝试的结果
                attempt_info = {
                    "attempt": attempt + 1,
                    "attempted_sql": corrected_sql,
                    "error_message": str(new_error)
                }

                if "attempts_details" not in self.error_history[-1]:
                    self.error_history[-1]["attempts_details"] = []

                self.error_history[-1]["attempts_details"].append(attempt_info)

                # 更新错误信息用于下一次尝试
                error = new_error
                sql = corrected_sql

        # 如果所有尝试都失败
        self.error_history[-1]["success"] = False
        self.error_history[-1]["attempts"] = self.max_correction_attempts

        return sql, None, error

    def _generate_corrected_sql(self, sql: str, error: Exception, question: str, attempt: int) -> str:
        """
        使用LLM生成修复后的SQL

        Args:
            sql: 有错误的SQL
            error: 捕获的异常
            question: 原始问题
            attempt: 当前尝试次数

        Returns:
            str: 修复后的SQL
        """
        error_message = str(error)
        error_type = type(error).__name__

        # 构建提示消息
        prompt = self._build_correction_prompt(sql, error_message, error_type, question, attempt)

        # 使用已有的LLM客户端获取修复建议
        corrected_sql = self._get_llm_correction(prompt)

        # 从回复中提取SQL (处理可能的说明文本)
        corrected_sql = self.vn.extract_sql(corrected_sql)

        # corrected_sql = add_limit_to_sql(corrected_sql, limit=1000)

        return corrected_sql

    def _build_correction_prompt(self, sql: str, error_message: str, error_type: str, question: str,
                                 attempt: int) -> list:
        """构建SQL纠错提示"""
        system_message = """##你是一个专业的SQL错误修复专家。你的任务是分析SQL查询错误并提供修复后的SQL。
##请仅返回修复后的SQL语句，不需要解释或注释。确保修复后的SQL语法正确且可执行。\n\n"""

        if self.vn.relevant_ddl_context is not None:
            system_message += f"##表结构信息如下：\n\n{self.vn.relevant_ddl_context}\n\n"

        # 如果是第二次或以后的尝试，添加更多上下文
        if attempt > 1:
            system_message += """
之前的修复尝试失败了，请更仔细地分析错误原因，并提供不同的解决方案。
考虑以下可能的问题：表名错误、列名错误、语法错误、数据类型不匹配等。"""

        user_content = f"""原始问题: {question}

错误的SQL查询:
```sql
{sql}
```

执行时出现的错误:
错误类型: {error_type}
错误信息: {error_message}

请提供修复后的SQL查询。只返回SQL语句本身，不要包含解释或其他文本。"""

        message_log = [
            self.vn.system_message(system_message),
            self.vn.user_message(user_content)
        ]

        return message_log

    def _get_llm_correction(self, prompt: list) -> str:
        """使用LLM获取SQL修复建议"""
        # 直接使用Vanna实例的submit_prompt方法
        response = self.vn.submit_prompt(prompt)
        return response

    def _extract_sql_from_response(self, response: str) -> str:
        """从LLM响应中提取SQL语句"""
        # 处理可能包含markdown代码块的情况
        if "```sql" in response and "```" in response.split("```sql", 1)[1]:
            sql_part = response.split("```sql", 1)[1].split("```", 1)[0].strip()
            return sql_part
        elif "```" in response and "```" in response.split("```", 1)[1]:
            sql_part = response.split("```", 1)[1].split("```", 1)[0].strip()
            return sql_part
        else:
            # 如果没有代码块标记，直接返回响应（假设响应就是SQL）
            return response.strip()

    def get_error_history(self) -> list:
        """获取错误历史记录"""
        return self.error_history
