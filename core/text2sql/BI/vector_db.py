import re
from typing import List

import pandas as pd
from langchain_openai import OpenAIEmbeddings
from pymilvus import MilvusClient
from vanna.base import VannaBase
from vanna.utils import deterministic_uuid

import api.endpoints.conversations
from core.config import model_config
from core.config.vector_config import embedding_model_name, embedding_model_api_base
from core.utlis import extract_table_names
from core.config.logging import logger


class MilvusVectorDB(VannaBase):

    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)
        if config is None:
            config = {}

        self.embedding_function = OpenAIEmbeddings(model=embedding_model_name,
                                                   openai_api_base=embedding_model_api_base,
                                                   openai_api_key="...")

        self.n_results_sql = config.get("n_results_sql", config.get("n_results", 10))
        self.n_results_documentation = config.get("n_results_documentation", config.get("n_results", 10))
        self.n_results_ddl = config.get("n_results_ddl", config.get("n_results", 10))

        self.milvus_client = MilvusClient(uri="http://172.27.9.4:19530", token="root:Milvus", db_name="Text2Sql")

    def add_ddl(self, ddl: str, **kwargs) -> str:
        id = deterministic_uuid(ddl) + "-ddl"
        self.milvus_client.insert(
            collection_name="bi_ddl",
            data=[
                {
                    "ids": id,
                    "documents": ddl,
                    "documents_summary": kwargs.get("documents_summary"),
                    "table_name": kwargs.get('table_name') if kwargs.get('table_name') else ",".join(
                        extract_table_names(ddl)),
                    "embeddings": self.generate_embedding(kwargs.get("documents_summary")),
                }
            ]
        )
        return id

    def add_documentation(self, documentation: str, **kwargs) -> str:
        id = deterministic_uuid(documentation) + "-doc"
        self.milvus_client.insert(
            collection_name="bi_documentation",
            data=[
                {
                    "ids": id,
                    "documents": documentation,
                    "embeddings": self.generate_embedding(documentation),
                }
            ]
        )
        return id

    def add_question_sql(self, question: str, sql: str, **kwargs) -> str:
        id = deterministic_uuid(question) + "-sql"
        self.milvus_client.insert(
            collection_name="bi_sql",
            data=[
                {
                    "ids": id,
                    "question": question,
                    "sql": sql,
                    "embeddings": self.generate_embedding(question),
                    "table_name": ",".join(extract_table_names(sql))
                }
            ])
        return id

    def get_similar_question_sql(self, question: str, **kwargs) -> list:
        _vector = self.generate_embedding(question)
        out = []

        res = self.milvus_client.search(
            collection_name="bi_sql",
            anns_field="embeddings",
            data=[_vector],
            limit=self.n_results_sql,
            params={"metric_type": "L2"},
            output_fields=["ids", "question", "sql"],
            filter=kwargs.get("where") if kwargs.get("where") else ''
        )
        for hits in res:
            for hit in hits:
                out.append({"question": hit["entity"]["question"], "sql": hit["entity"]["sql"]})

        return out

    def get_related_ddl(self, question: str, **kwargs) -> list:
        out = []
        try:
            logger.info("使用LLM召回DDL信息...")
            res = self.milvus_client.query(
                collection_name="bi_ddl",
                filter=kwargs.get("where") if kwargs.get("where") else '',
                output_fields=["ids", "table_name", "documents_summary", "documents"],
                limit=10
            )

            table_name_summary = {}
            table_info = {}

            for item in res:
                table_name_summary[item["table_name"]] = item["documents_summary"]
                table_info[item["table_name"]] = {"documents": item["documents"],
                                                  "documents_summary": item["documents_summary"]}
            # logger.debug(table_info)
            message = [
                {
                    "role": "user",
                    "content": f"根据问题输入，选择与问题可能相关的表名称，只返回表名称，至少返回2张名称，结果用列表包括，例如：[表1,表2]\n\n### 表名称与描述\n{table_name_summary}\n问题：{question} /no_think"
                }
            ]
            # logger.debug(message)
            responses = api.endpoints.conversations.chat.completions.create(
                model=model_config.no_think_model_name,
                messages=message,
                stream=False,
                temperature=model_config.no_think_model_temperature,
                top_p=model_config.no_think_model_top_p,
                max_tokens=512,
            )
            table_name = responses.choices[0].message.content.replace("\n", "").replace("<think></think>", "")
            table_name = re.findall(r'\w+', table_name)
            for t in table_name:
                out.append(
                    f'【表描述】\n{table_info[t]["documents_summary"]}\n【表信息】\n{table_info[t]["documents"]}\n')

        except Exception as e:
            import traceback
            logger.error("模型召回失败，使用向量召回DDL: " + str(e))
            logger.error(traceback.format_exc())

            _vector = self.generate_embedding(question)

            res = self.milvus_client.search(
                collection_name="bi_ddl",
                anns_field="embeddings",
                data=[_vector],
                limit=self.n_results_ddl,
                params={"metric_type": "L2"},
                output_fields=["ids", "documents", "documents_summary"],
                filter=kwargs.get("where") if kwargs.get("where") else ''
            )
            for hits in res:
                for hit in hits:
                    out.append(
                        f'【表描述】\n{hit["entity"]["documents_summary"]}\n【表信息】\n{hit["entity"]["documents"]}\n')

        return out

    def get_related_documentation(self, question: str, **kwargs) -> list:
        _vector = self.generate_embedding(question)
        out = []

        res = self.milvus_client.search(
            collection_name="bi_documentation",
            anns_field="embeddings",
            data=[_vector],
            limit=self.n_results_documentation,
            params={"metric_type": "L2"},
            output_fields=["ids", "documents"],
            filter=kwargs.get("where") if kwargs.get("where") else ''
        )
        for hits in res:
            for hit in hits:
                out.append(hit["entity"]["documents"])

        return out

    def get_training_data(self, **kwargs) -> pd.DataFrame:
        df = pd.DataFrame()
        iterator = self.milvus_client.query_iterator(
            collection_name="bi_sql",
            filter="",
            output_fields=["ids", "question", "sql"],
            batch_size=100
        )

        ids, question, sql = [], [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                question.append(item["question"])
                sql.append(item["sql"])

        # Create a DataFrame
        df_sql = pd.DataFrame(
            {
                "id": ids,
                "question": question,
                "content": sql,
            }
        )

        df_sql["training_data_type"] = "sql"

        df = pd.concat([df, df_sql])

        iterator = self.milvus_client.query_iterator(
            collection_name="bi_ddl",
            filter="",
            output_fields=["ids", "documents", "documents_summary"],
            batch_size=100
        )
        ids, documents = [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                documents.append(item["documents_summary"] + item["documents"])

        # Create a DataFrame
        df_ddl = pd.DataFrame(
            {
                "id": ids,
                "question": [None for doc in documents],
                "content": documents,
            }
        )

        df_ddl["training_data_type"] = "ddl"

        df = pd.concat([df, df_ddl])

        iterator = self.milvus_client.query_iterator(
            collection_name="bi_documentation",
            filter="",
            output_fields=["ids", "documents"],
            batch_size=100
        )
        ids, documents = [], []

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                ids.append(item["ids"])
                documents.append(item["documents"])

        # Create a DataFrame
        df_doc = pd.DataFrame(
            {
                "id": ids,
                "question": [None for doc in documents],
                "content": documents,
            }
        )

        df_doc["training_data_type"] = "documentation"

        df = pd.concat([df, df_doc])

        return df

    def remove_training_data(self, id: str, **kwargs) -> bool:
        if id.endswith("-sql"):
            self.milvus_client.delete(
                collection_name="bi_sql",
                filter=f"ids == '{id}'"
            )
            return True
        elif id.endswith("-ddl"):
            self.milvus_client.delete(
                collection_name="bi_ddl",
                filter=f"ids == '{id}'"
            )
            return True
        elif id.endswith("-doc"):
            self.milvus_client.delete(
                collection_name="bi_documentation",
                filter=f"ids == '{id}'"
            )
            return True
        else:
            return False

    # def system_message(self, message: str) -> any:
    #     return {"role": "system", "content": message}
    #
    # def user_message(self, message: str) -> any:
    #     return {"role": "user", "content": message}
    #
    # def assistant_message(self, message: str) -> any:
    #     return {"role": "assistant", "content": message}

    def generate_embedding(self, data: str, **kwargs) -> List[float]:
        return self.embedding_function.embed_query(data)
