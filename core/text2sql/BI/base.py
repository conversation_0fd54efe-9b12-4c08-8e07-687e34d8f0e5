import json
from typing import Union
import traceback
import openai
import pandas as pd
import plotly
from vanna.base import VannaBase
import plotly.express as px
import plotly.graph_objects as go
from core.config.database_config import table_permission_map
from core.config.logging import logger
from core.text2sql.BI.vector_db import MilvusVectorDB
from core.utlis import extract_table_names


class CustomVannaBase(VannaBase):
    def __init__(self, config=None):
        # Implement here
        super().__init__(config)
        # deepseek官方
        # self.client = openai.Client(api_key="sk-6d615a03db4540299876f8daf3b5ed2c", base_url="https://api.deepseek.com")
        # self.model = "deepseek-chat"
        # 自建
        self.client = openai.Client(api_key="...", base_url="http://10.26.66.2:9100/v1")
        self.model = "qwen2.5-coder-32B-GPTQ-INT8"
        self.relevant_ddl_context = None

    def ask(
            self,
            question: Union[str, None] = None,
            print_results: bool = False,
            auto_train: bool = False,
            visualize: bool = False,  # if False, will not generate plotly code
            allow_llm_to_see_data: bool = False,
            role=None,
    ):
        if role is None:
            role = []

        # 权限模块
        allow_table = []
        for r in role:
            name = r["name"]
            if name in table_permission_map.keys():
                allow_table += table_permission_map[name]
        allow_table = list(set(allow_table))

        training_data = self.get_training_data()
        allow_id_list = []
        for index, row in training_data.iterrows():
            if row["training_data_type"] == "sql":
                id = row["id"]
                _ = row["question"]
                sql = row["content"]
                table_name = extract_table_names(sql)

                allow = all(elem in allow_table for elem in table_name)
                if allow:
                    allow_id_list.append(id)

            elif row["training_data_type"] == "ddl":
                id = row["id"]
                content = row["content"]

                for x in allow_table:
                    if x in content:
                        allow_id_list.append(id)
                        break
            else:
                pass

        where = f"ids in {allow_id_list}"
        # logger.debug(allow_table)
        # logger.debug(where)

        if question is None:
            question = input("Enter a question: ")

        try:
            sql = self.generate_sql(question=question, allow_llm_to_see_data=allow_llm_to_see_data,
                                    where=where)

        except Exception as e:
            logger.error(f"错误信息：{e}")
            logger.error(traceback.format_exc())
            return None, None, None, ""

        if self.run_sql_is_set is False:
            logger.warning(
                "If you want to run the SQL query, connect to a database first."
            )

            if print_results:
                return None
            else:
                return sql, None, None

        try:
            # 在执行SQL前添加LIMIT限制
            # sql = add_limit_to_sql(sql, limit=1000)
            fig_json = ""
            df = self.run_sql(sql)
            if visualize:
                try:
                    plotly_code = self.generate_plotly_code(
                        question=question,
                        sql=sql,
                        df_metadata=f"Running df.dtypes gives:\n {df.dtypes}",
                        df_head=df.head(),
                        df=df
                    )
                    # df.to_csv("./df.csv")
                    # logger.debug(f"绘图code: \n{plotly_code}")
                    if plotly_code != "无法绘图":
                        fig = self.get_plotly_figure(plotly_code=plotly_code, df=df, dark_mode=False)
                        if fig is not None:
                            fig_json = fig.to_json()

                except Exception as e:
                    # Print stack trace
                    traceback.print_exc()
                    print("Couldn't run plotly code: ", e)
                    if print_results:
                        return None
                    else:
                        return sql, df, None, ""
            else:
                return sql, df, None, ""

        except Exception as e:
            logger.warning("Couldn't run sql: ", e)
            return sql, None, e, ""
        return sql, df, None, fig_json

    def system_message(self, message: str) -> any:
        return {"role": "system", "content": message}

    def user_message(self, message: str) -> any:
        return {"role": "user", "content": message}

    def assistant_message(self, message: str) -> any:
        return {"role": "assistant", "content": message}

    def generate_sql(self, question: str, **kwargs) -> str:
        # Use the super generate_sql
        sql = super().generate_sql(question, **kwargs)

        # Replace "\_" with "_"
        sql = sql.replace("\\_", "_")

        return sql

    def add_documentation_to_prompt(
            self,
            initial_prompt: str,
            documentation_list: list[str],
            max_tokens: int = 14000,
    ) -> str:
        if len(documentation_list) > 0:
            initial_prompt += "\n===提示信息 术语表 \n\n"

            for documentation in documentation_list:
                if (
                        self.str_to_approx_token_count(initial_prompt)
                        + self.str_to_approx_token_count(documentation)
                        < max_tokens
                ):
                    initial_prompt += f"{documentation}\n\n"

        return initial_prompt

    def add_ddl_to_prompt(
            self, initial_prompt: str, ddl_list: list[str], max_tokens: int = 14000
    ) -> str:
        if len(ddl_list) > 0:
            initial_prompt += "### PostgreSQL表及其属性如下：\n"

            for ddl in ddl_list:
                if (
                        self.str_to_approx_token_count(initial_prompt)
                        + self.str_to_approx_token_count(ddl)
                        < max_tokens
                ):
                    initial_prompt += f"{ddl}\n\n"

        return initial_prompt

    def get_sql_prompt(
            self,
            initial_prompt: str,
            question: str,
            question_sql_list: list,
            ddl_list: list,
            doc_list: list,
            **kwargs,
    ):
        if initial_prompt is None:
            initial_prompt = """### 请将以下自然语言查询转换为一个精确的PostgreSQL语句"""

        initial_prompt = self.add_ddl_to_prompt(
            initial_prompt, ddl_list, max_tokens=self.max_tokens
        )

        if self.static_documentation != "":
            doc_list.append(self.static_documentation)

        initial_prompt = self.add_documentation_to_prompt(
            initial_prompt, doc_list, max_tokens=self.max_tokens
        )

        self.relevant_ddl_context = initial_prompt

        message_log = [self.system_message(initial_prompt)]

        # 反转question_sql_list，让更相似的问答对接近用户提问
        question_sql_list.reverse()

        for example in question_sql_list:
            if example is None:
                print("example is None")
            else:
                if example is not None and "question" in example and "sql" in example:
                    message_log.append(self.user_message(example["question"]))
                    message_log.append(self.assistant_message(example["sql"]))

        message_log.append(self.user_message(question))
        # 只输出用户消息
        logger.debug(f"召回历史问答对: \n{[x for x in message_log if x['role'] == 'user']}")
        recall_ddl = [x[:30] for x in ddl_list]
        logger.debug(f"召回DDL: \n{recall_ddl}")
        return message_log

    def submit_prompt(self, prompt, **kwargs) -> str:
        chat_response = self.client.chat.completions.create(
            model=self.model,
            messages=prompt,
            temperature=0.3,
            # top_p=0.8,
            max_tokens=4096
        )

        return chat_response.choices[0].message.content

    def get_plotly_figure(
            self, plotly_code: str, df: pd.DataFrame, dark_mode: bool = True
    ) -> plotly.graph_objs.Figure:
        """
        Get a Plotly figure from a dataframe and Plotly code.

        Args:
            df (pd.DataFrame): The dataframe to use.
            plotly_code (str): The Plotly code to use.

        Returns:
            plotly.graph_objs.Figure: The Plotly figure.
        """
        ldict = {"df": df, "px": px, "go": go}
        try:
            exec(plotly_code, globals(), ldict)

            fig = ldict.get("fig", None)

            return fig

        except Exception as e:

            return None


    def generate_plotly_code(
            self, question: str = None, sql: str = None, df_metadata: str = None,
            df_head: str = None, df=None, **kwargs
    ) -> str:
        """
        生成Plotly图表代码

        Args:
            question: 用户提出的问题
            sql: 生成数据的SQL查询
            df_metadata: DataFrame的元数据信息
            df_head: DataFrame的示例数据
            df: 实际的DataFrame对象(可选)

        Returns:
            str: 生成的Plotly代码或"无法绘图"
        """
        # 检查数据是否适合可视化
        if df is not None and not self._validate_data_for_visualization(df):
            return "无法绘图"

        # 获取数据示例
        if df_head is None and df is not None:
            df_head = self._get_df_sample(df)
        elif df_head is None:
            df_head = "无法绘图"

        # 构建系统消息
        system_msg = self._build_system_message(question, sql, df_metadata, df_head)

        # 构建提示词
        user_prompt = """生成用于绘图的 Plotly Python 代码，以展示一个名为 df 的 pandas 数据框中的数据。如果数据框中只有一个值（例如只有一行一列），则使用 Indicator 图；否则根据数据内容生成合适的图表。返回结果时 只输出代码，不加任何解释说明。"""

        message_log = [
            self.system_message(system_msg),
            self.user_message(user_prompt),
        ]

        try:
            plotly_code = self.submit_prompt(message_log, kwargs=kwargs)

            # 检查返回结果
            if "无法绘图" in plotly_code:
                return "无法绘图"

            # 清理和验证代码
            clean_code = self._sanitize_plotly_code(self._extract_python_code(plotly_code))

            return clean_code

        except Exception as e:
            return "无法绘图"

    def _build_system_message(self, question: str, sql: str, df_metadata: str, df_head: str) -> str:
        """构建系统消息"""
        if question is not None:
            system_msg = f"以下是一个pandas DataFrame，包含回答问题的查询结果：'{question}'"
        else:
            system_msg = "以下是一个pandas DataFrame的信息"

        if sql is not None:
            system_msg += f"\n\nDataFrame通过以下SQL查询生成：\n{sql}\n"

        system_msg += f"\nDataFrame信息：\n{df_metadata}"
        system_msg += f"\n\n数据示例（df.head()）：\n{df_head}"

        system_msg += """
请根据DataFrame内容生成合适的Plotly图表代码。

要求：
1. 数据在名为'df'的pandas DataFrame中
2. 只允许使用以下图表类型：饼图(pie)、柱状图(bar)、折线图(line)或指示器(indicator)
3. 如果数据无法可视化，请直接返回："无法绘图"
4. 只返回Python代码，不要包含任何解释
5. 请根据数据特征选择最合适的图表类型
6. 添加中文标题和标签
7. 设置合适的颜色方案

图表选择指南：
- 单个数值 → 使用指示器(indicator)
- 分类数据占比 → 使用饼图(pie)
- 分类数据比较 → 使用柱状图(bar)
- 时间序列/趋势 → 使用折线图(line)
""".lstrip()

        return system_msg

    def _validate_plotly_code(self, code: str) -> bool:
        """验证生成的Plotly代码"""
        try:
            # 检查是否包含必要的导入和基本结构
            required_patterns = [
                'import',
                'plotly',
                'fig',
                'show()'
            ]

            # 检查是否只使用允许的图表类型
            allowed_patterns = ['px.pie', 'px.bar', 'px.line', 'go.Indicator']

            for pattern in required_patterns:
                if pattern not in code:
                    return False

            # 检查是否使用了允许的图表类型
            has_allowed_chart = any(pattern in code for pattern in allowed_patterns)
            if not has_allowed_chart:
                return False

            return True

        except Exception:
            return False

    def _get_df_sample(self, df) -> str:
        """获取DataFrame的示例数据"""
        try:
            import pandas as pd

            if not isinstance(df, pd.DataFrame):
                return "无法获取数据示例"

            # 获取基本信息
            info = []
            info.append(f"形状: {df.shape}")
            info.append(f"列名: {', '.join(df.columns.tolist())}")
            info.append(f"数据类型: {df.dtypes.to_dict()}")
            info.append("\n数据示例:")
            info.append(df.head(5).to_string())

            # 如果有数值列，添加统计信息
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                info.append("\n基本统计:")
                info.append(df.describe().to_string())

            return "\n".join(info)

        except Exception:
            return "无法获取数据示例"

    def _validate_data_for_visualization(self, df) -> bool:
        """验证数据是否适合可视化"""
        try:
            import pandas as pd

            if not isinstance(df, pd.DataFrame):
                return False

            # 检查数据是否为空
            if df.empty or len(df) == 0:
                return False

            # 检查是否有可绘制的列
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) == 0:
                # 如果没有数值列，检查是否有分类数据可以计数
                categorical_cols = df.select_dtypes(include=['object', 'category']).columns
                if len(categorical_cols) == 0:
                    return False

            return True

        except Exception:
            return False


class Vanna(CustomVannaBase, MilvusVectorDB):
    def __init__(self, config=None):
        CustomVannaBase.__init__(self, config=config)
        MilvusVectorDB.__init__(self, config=config)
