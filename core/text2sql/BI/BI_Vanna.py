from typing import Union, Tuple, Optional, Any

import pandas as pd
from chromadb.utils import embedding_functions
from pandas import <PERSON>Frame

from core.config.logging import logger
from core.text2sql.BI.base import <PERSON><PERSON>
from core.text2sql.correction_module import SQLCorrector
from prompts.prompt_manager import TEXT2SQL_BI_PROMPT


# 扩展MyVanna类，整合SQL纠错功能
class BIVannaWithCorrection(Vanna):

    def __init__(self,
                 db_cache_path="/home/<USER>/zhouhan/code/GC-ES-LLM/core/text2sql/BI/cache",
                 host='***********',
                 dbname='gc_cw',
                 user='gccw',
                 password='GcW@0319',
                 port=5432):

        config = {"path": db_cache_path,
                  "embedding_function": embedding_functions.OpenAIEmbeddingFunction(
                      api_key="...",
                      api_base="http://**********:9997/v1",
                      model_name="bge-large-zh-v1.5"
                  ),
                  "n_results_sql": 3,
                  "n_results_ddl": 2,
                  "n_results_doc": 1,
                  "initial_prompt": TEXT2SQL_BI_PROMPT}

        super().__init__(config=config)
        self.sql_corrector = SQLCorrector(self)
        self.connect_to_postgres(host=host, dbname=dbname, user=user, password=password, port=port)

    def ask_with_correction(
            self,
            question: Union[str, None] = None,
            print_results: bool = False,
            auto_train: bool = False,
            visualize: bool = False,
            allow_llm_to_see_data: bool = False,
            enable_correction: bool = True,
            role=None,

    ) -> tuple[str, DataFrame, Any, str] | tuple[Any, Any, Any, Any]:
        """
        扩展ask方法，添加自动SQL纠错功能

        Args:
            question: 用户问题
            print_results: 是否打印结果
            auto_train: 是否自动训练
            visualize: 是否生成可视化代码
            allow_llm_to_see_data: 是否允许LLM查看数据
            enable_correction: 是否启用SQL纠错
            role: 用户角色

        Returns:
            tuple: (SQL查询, 结果DataFrame, 异常(如果有))
        """
        # 调用原始ask方法
        # logger.debug("ask")
        sql, df, error, fig_json = super().ask(
            question=question,
            print_results=False,  # 先不打印结果
            auto_train=False,
            visualize=True,
            allow_llm_to_see_data=False,
            role=role,
        )

        if not sql:
            return "", pd.DataFrame(), error, ""

        if enable_correction and error is not None and sql is not None:
            logger.info(f"检测到SQL错误，尝试自动修复...\n原始错误: {error}")

            # 尝试修复SQL
            corrected_sql, corrected_df, corrected_error = self.sql_corrector.correct_sql(
                question=question if question is not None else "",
                sql=sql,
                error=error,
                role=role
            )

            if corrected_error is None:
                logger.info("SQL修复成功!")
                sql, df, error = corrected_sql, corrected_df, None
            else:
                logger.info(f"SQL修复失败: {corrected_error}")
                sql, df, error = corrected_sql, None, corrected_error

        return sql, df, error, fig_json


