import json
from typing import Union

import openai
from vanna.base import VannaBase
from vanna.chromadb import ChromaDB_VectorStore
from vanna.utils import deterministic_uuid

from core.config.database_config import table_permission_map
from core.config.logging import logger
from core.text2sql.vector_db import MilvusVectorDB
from core.utlis import extract_table_names


class CustomVannaBase(VannaBase):
    def __init__(self, config=None):
        # Implement here
        super().__init__(config)
        # deepseek官方
        # self.client = openai.Client(api_key="sk-6d615a03db4540299876f8daf3b5ed2c", base_url="https://api.deepseek.com")
        # self.model = "deepseek-chat"
        # 自建
        self.client = openai.Client(api_key="...", base_url="http://10.26.66.2:9100/v1")
        self.model = "qwen2.5-coder-32B-GPTQ-INT8"
        self.relevant_ddl_context = None

    def ask(
            self,
            question: Union[str, None] = None,
            print_results: bool = False,
            auto_train: bool = False,
            visualize: bool = False,  # if False, will not generate plotly code
            allow_llm_to_see_data: bool = False,
            role=None,
    ):
        if role is None:
            role = []

        # 权限模块
        allow_table = []
        for r in role:
            name = r["name"]
            if name in table_permission_map.keys():
                allow_table += table_permission_map[name]
        allow_table = list(set(allow_table))

        training_data = self.get_training_data()
        allow_id_list = []
        for index, row in training_data.iterrows():
            if row["training_data_type"] == "sql":
                id = row["id"]
                _ = row["question"]
                sql = row["content"]
                table_name = extract_table_names(sql)

                allow = all(elem in allow_table for elem in table_name)
                if allow:
                    allow_id_list.append(id)

            elif row["training_data_type"] == "ddl":
                id = row["id"]
                content = row["content"]

                for x in allow_table:
                    if x in content:
                        allow_id_list.append(id)
                        break
            else:
                pass

        where = f"ids in {allow_id_list}"
        # logger.debug(allow_table)
        # logger.debug(where)

        if question is None:
            question = input("Enter a question: ")

        try:
            sql = self.generate_sql(question=question, allow_llm_to_see_data=allow_llm_to_see_data,
                                    where=where)

        except Exception as e:
            import traceback
            logger.error(f"错误信息：{e}")
            logger.error(traceback.format_exc())
            return None, None, None

        if self.run_sql_is_set is False:
            logger.warning(
                "If you want to run the SQL query, connect to a database first."
            )

            if print_results:
                return None
            else:
                return sql, None, None

        try:
            # 在执行SQL前添加LIMIT限制
            # sql = add_limit_to_sql(sql, limit=1000)

            df = self.run_sql(sql)
            # 权限校验模块
            # if not check_db_permission(sql, role):
            #     logger.warning(f"用户角色{role}, sql: {sql}, \n 暂无数据库访问权限！请联系管理员后重试！")
            #     return None, None, "暂无数据访问权限！请联系管理员后重试！"

            if len(df) > 0 and auto_train:
                self.add_question_sql(question=question, sql=sql)
            else:
                return sql, df, None

        except Exception as e:
            logger.warning("Couldn't run sql: ", e)
            return sql, None, e
        return sql, df, None

    def system_message(self, message: str) -> any:
        return {"role": "system", "content": message}

    def user_message(self, message: str) -> any:
        return {"role": "user", "content": message}

    def assistant_message(self, message: str) -> any:
        return {"role": "assistant", "content": message}

    def generate_sql(self, question: str, **kwargs) -> str:
        # Use the super generate_sql
        sql = super().generate_sql(question, **kwargs)

        # Replace "\_" with "_"
        sql = sql.replace("\\_", "_")

        return sql

    def add_documentation_to_prompt(
            self,
            initial_prompt: str,
            documentation_list: list[str],
            max_tokens: int = 14000,
    ) -> str:
        if len(documentation_list) > 0:
            initial_prompt += "\n===提示信息 术语表 \n\n"

            for documentation in documentation_list:
                if (
                        self.str_to_approx_token_count(initial_prompt)
                        + self.str_to_approx_token_count(documentation)
                        < max_tokens
                ):
                    initial_prompt += f"{documentation}\n\n"

        return initial_prompt

    def add_ddl_to_prompt(
            self, initial_prompt: str, ddl_list: list[str], max_tokens: int = 14000
    ) -> str:
        if len(ddl_list) > 0:
            initial_prompt += "### PostgreSQL表及其属性如下：\n"

            for ddl in ddl_list:
                if (
                        self.str_to_approx_token_count(initial_prompt)
                        + self.str_to_approx_token_count(ddl)
                        < max_tokens
                ):
                    initial_prompt += f"{ddl}\n\n"

        return initial_prompt

    def get_sql_prompt(
            self,
            initial_prompt: str,
            question: str,
            question_sql_list: list,
            ddl_list: list,
            doc_list: list,
            **kwargs,
    ):
        if initial_prompt is None:
            initial_prompt = """### 请将以下自然语言查询转换为一个精确的PostgreSQL语句"""

        initial_prompt = self.add_ddl_to_prompt(
            initial_prompt, ddl_list, max_tokens=self.max_tokens
        )

        if self.static_documentation != "":
            doc_list.append(self.static_documentation)

        initial_prompt = self.add_documentation_to_prompt(
            initial_prompt, doc_list, max_tokens=self.max_tokens
        )

        self.relevant_ddl_context = initial_prompt

        message_log = [self.system_message(initial_prompt)]

        for example in question_sql_list:
            if example is None:
                print("example is None")
            else:
                if example is not None and "question" in example and "sql" in example:
                    message_log.append(self.user_message(example["question"]))
                    message_log.append(self.assistant_message(example["sql"]))

        message_log.append(self.user_message(question))
        # 只输出用户消息
        logger.debug(f"召回内容: {[x for x in message_log if x['role'] == 'user']}")
        recall_ddl = [x[:10] for x in ddl_list]
        logger.debug(f"召回DDL: {recall_ddl}")
        return message_log

    def submit_prompt(self, prompt, **kwargs) -> str:
        chat_response = self.client.chat.completions.create(
            model=self.model,
            messages=prompt,
            temperature=0.3,
            top_p=1,
            max_tokens=4096
        )

        return chat_response.choices[0].message.content


class Vanna(CustomVannaBase, MilvusVectorDB):
    def __init__(self, config=None):
        CustomVannaBase.__init__(self, config=config)
        MilvusVectorDB.__init__(self, config=config)