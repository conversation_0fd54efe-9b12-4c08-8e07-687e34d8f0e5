from typing import List, Dict

from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
import re

from core.config.database_config import table_permission_map
from core.config.logging import logger


def convert_to_openai_format(messages) -> List[Dict[str, str]]:
    openai_messages = []
    for msg in messages:
        if isinstance(msg, SystemMessage):
            openai_messages.append({"role": "system", "content": msg.content})
        elif isinstance(msg, AIMessage):
            openai_messages.append({"role": "assistant", "content": msg.content})
        elif isinstance(msg, HumanMessage):
            openai_messages.append({"role": "user", "content": msg.content})
    return openai_messages


def extract_table_names(sql_query):
    """
    从SQL查询语句中提取真实的表名，忽略CTE（WITH子句）中的临时表名

    参数:
        sql_query (str): SQL查询语句

    返回:
        list: 提取到的真实表名列表
    """
    # 将查询字符串转换为小写以便统一处理
    sql_query = sql_query.lower()

    # 删除SQL注释
    sql_query = re.sub(r'--.*?(\n|$)', ' ', sql_query)
    sql_query = re.sub(r'/\*.*?\*/', ' ', sql_query, flags=re.DOTALL)

    # 提取CTE名称（with 子句中定义的临时表名）
    cte_regex = r'with\s+([a-zA-Z0-9_]+)\s+as\s*\('
    cte_names = re.findall(cte_regex, sql_query)

    # 匹配 FROM 和 JOIN 后的表名（包括可能的别名）
    from_regex = r'from\s+([a-zA-Z0-9_\.]+)(?:\s+(?:as\s+)?[a-zA-Z0-9_]+)?'
    join_regex = r'join\s+([a-zA-Z0-9_\.]+)(?:\s+(?:as\s+)?[a-zA-Z0-9_]+)?'

    from_tables = re.findall(from_regex, sql_query)
    join_tables = re.findall(join_regex, sql_query)

    # 合并表名并处理schema.table形式
    tables = []
    for table in from_tables + join_tables:
        # 如果是 schema.table，只取 table 名字部分
        if '.' in table:
            table = table.split('.', 1)[1]
        tables.append(table)

    # 去除 CTE 名称和重复项
    real_tables = [table for table in set(tables) if table not in cte_names]

    return real_tables


def check_db_permission(sql, role):
    allow_table = []
    for r in role:
        name = r["name"]
        if name in table_permission_map.keys():
            allow_table += table_permission_map[name]
    extracted_tables = extract_table_names(sql)

    allow_table_set = set(allow_table)
    extracted_tables_set = set(extracted_tables)
    logger.info("======")
    logger.info(f"allow_table_set: {allow_table_set}, extracted_tables_set: {extracted_tables_set}")

    if extracted_tables_set.issubset(allow_table_set):
        return True
    else:
        return False


def add_limit_to_sql(sql: str, limit: int = 1000) -> str:
    """
    如果SQL查询没有LIMIT子句，则添加LIMIT子句

    Args:
        sql: 原始SQL查询
        limit: 限制的行数，默认1000

    Returns:
        添加了LIMIT子句的SQL查询
    """
    # 去除多余空格并转换为大写以便于检查
    # 注意：这里只转换用于检查的副本，不改变原始SQL的大小写
    sql_upper = ' '.join(sql.split()).upper()

    # 检查SQL是否已经包含LIMIT子句
    if ' LIMIT ' not in sql_upper and not sql_upper.endswith(' LIMIT'):
        # 检查是否包含分号结尾
        if sql.strip().endswith(';'):
            sql = sql.strip()[:-1] + f" LIMIT {limit};"
        else:
            sql = sql.strip() + f" LIMIT {limit}"

    return sql


if __name__ == '__main__':
    print(extract_table_names("""
    WITH projects_2025 AS (
  -- 选取2025年的项目，提取唯一项目编号
  SELECT DISTINCT item_number
  FROM ods_fs_yf_poject_solution
  WHERE partition_time LIKE '2025%'
)
SELECT 
  p.solution,
  COUNT(DISTINCT p.item_number) AS solution_count
FROM ods_fs_yf_poject_solution_proportion p
INNER JOIN projects_2025 proj 
  ON p.item_number = proj.item_number
GROUP BY p.solution
ORDER BY solution_count DESC;
    """))