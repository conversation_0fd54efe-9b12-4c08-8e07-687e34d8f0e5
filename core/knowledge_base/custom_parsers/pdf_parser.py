import json
import os
from typing import Iterator
from typing import List, Dict, Any

import requests
from docling.document_converter import DocumentConverter
from langchain_core.document_loaders import BaseLoader
from langchain_core.documents import Document


def remove_empty_lines(text):
    lines = text.splitlines()
    non_empty_lines = [line for line in lines if line.strip()]
    return '\n'.join(non_empty_lines)


class CustomPdfLoader(BaseLoader):
    """An example document loader that reads a file line by line."""

    def __init__(self, file_path: str) -> None:
        """Initialize the loader with a file path.

        Args:
            file_path: The path to the file to load.
        """
        self.file_path = file_path
        self.converter = DocumentConverter()

    def lazy_load(self) -> Iterator[Document]:  # <-- Does not take any arguments
        """A lazy loader that reads a file line by line.

        When you're implementing lazy load methods, you should use a generator
        to yield documents one by one.
        """
        result = self.converter.convert(self.file_path)
        # 转换为markdown
        text = result.document.export_to_markdown()
        # 去除图片标记
        text = text.replace("<!-- image -->", "")
        # 去除空行
        text = remove_empty_lines(text)
        yield Document(
            page_content=text,
            metadata={"source": self.file_path},
        )


class PDFProcessingClient:
    """
    Python客户端用于MinerU服务端 处理PDF服务
    """

    def __init__(self, base_url: str = "http://**********:9918/"):
        """
        初始化客户端
        :param base_url: 服务的基础URL，默认为本地地址
        """
        self.base_url = base_url.rstrip('/')

    def process_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        上传PDF文件并处理
        :param pdf_path: PDF文件的本地路径
        :return: 响应数据，包含job_id和文件列表
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件未找到: {pdf_path}")

        # 确保文件是PDF
        if not pdf_path.lower().endswith('.pdf') and not pdf_path.lower().endswith('.doc') and not pdf_path.lower().endswith('.docx'):
            raise ValueError("文件只支持pdf、doc、docx格式")

        # 准备上传文件
        files = {'file': (os.path.basename(pdf_path), open(pdf_path, 'rb'), 'application/pdf')}

        # 调用处理接口
        response = requests.post(f"{self.base_url}/process_pdf/", files=files)

        # 关闭文件
        files['file'][1].close()

        # 检查响应
        if response.status_code != 200:
            raise Exception(f"处理失败，状态码: {response.status_code}, 响应: {response.text}")

        return response.json()

    def get_job_files(self, job_id: str) -> Dict[str, Any]:
        """
        获取指定任务的所有文件
        :param job_id: 任务ID
        :return: 文件列表
        """
        response = requests.get(f"{self.base_url}/jobs/{job_id}")

        if response.status_code != 200:
            raise Exception(f"获取任务文件失败，状态码: {response.status_code}, 响应: {response.text}")

        return response.json()

    def download_file(self, file_url: str, save_path: str) -> str:
        """
        下载单个文件
        :param file_url: 文件URL路径（相对路径，如/download/job_id/filename）
        :param save_path: 文件保存路径
        :return: 保存后的文件完整路径
        """
        # 确保URL以/开头，且不以/结尾
        if not file_url.startswith('/'):
            file_url = '/' + file_url
        if file_url.endswith('/'):
            file_url = file_url[:-1]

        # 确保URL使用正斜杠而非反斜杠
        file_url = file_url.replace('\\', '/')

        # 构建完整URL
        full_url = f"{self.base_url}{file_url}"

        print(f"Downloading from URL: {full_url}")

        # 发送请求下载文件
        response = requests.get(full_url, stream=True)

        if response.status_code != 200:
            raise Exception(f"下载文件失败，状态码: {response.status_code}, 响应: {response.text}")

        # 确保目标目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)

        # 保存文件
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        return os.path.abspath(save_path)

    def download_all_files(self, job_id: str, output_dir: str) -> List[str]:
        """
        下载任务的所有文件
        :param job_id: 任务ID
        :param output_dir: 输出目录
        :return: 下载的文件路径列表
        """
        # 获取任务文件列表
        job_data = self.get_job_files(job_id)
        print(f"获取到任务文件: {json.dumps(job_data.get('files', []), indent=2, ensure_ascii=False)}")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        downloaded_files = []
        failed_files = []

        # 下载每个文件
        for file_url in job_data.get('files', []):
            # 从URL中提取文件名
            parts = file_url.replace('\\', '/').split('/')
            filename = parts[-1]

            # 如果URL包含特定子目录，则创建对应的本地子目录结构
            sub_path = ""
            for part in parts:
                if part and part != "download" and part != job_id and part != filename:
                    sub_path = os.path.join(sub_path, part)

            if sub_path:
                save_dir = os.path.join(output_dir, sub_path)
                os.makedirs(save_dir, exist_ok=True)
                save_path = os.path.join(save_dir, filename)
            else:
                save_path = os.path.join(output_dir, filename)

            # 下载文件
            try:
                print(f"下载文件: {file_url} 到 {save_path}")
                downloaded_file = self.download_file(file_url, save_path)
                downloaded_files.append(downloaded_file)
            except Exception as e:
                print(f"❌ 下载文件失败 {file_url}: {str(e)}")
                failed_files.append(file_url)
                # 继续下载其他文件，而不是中断整个过程
                continue

        # 打印下载结果
        if failed_files:
            print(f"下载完成，成功: {len(downloaded_files)}，失败: {len(failed_files)}")
            print("失败的文件:")
            for f in failed_files:
                print(f" - {f}")
        else:
            print(f"所有文件下载成功，共 {len(downloaded_files)} 个文件")

        return downloaded_files

    def check_service_health(self) -> bool:
        """
        检查服务健康状态
        :return: 服务是否健康运行
        """
        try:
            response = requests.get(f"{self.base_url}/")
            return response.status_code == 200 and response.json().get('status') == 'OK'
        except Exception:
            return False


# PDFProcessingClient 类需要更新为支持异步操作
class AsyncPDFProcessingClient:
    """
    异步Python客户端用于MinerU服务端处理PDF服务
    """

    def __init__(self, base_url: str = "http://**********:9918/"):
        """
        初始化客户端
        :param base_url: 服务的基础URL，默认为本地地址
        """
        self.base_url = base_url.rstrip('/')

    async def process_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        异步上传PDF文件并处理
        :param pdf_path: PDF文件的本地路径
        :return: 响应数据，包含job_id和文件列表
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件未找到: {pdf_path}")

        # 确保文件是PDF、DOC或DOCX
        if not pdf_path.lower().endswith(('.pdf', '.doc', '.docx')):
            raise ValueError("文件只支持pdf、doc、docx格式")

        # 使用aiohttp进行异步请求
        import aiohttp
        import aiofiles

        async with aiohttp.ClientSession() as session:
            # 读取文件内容
            async with aiofiles.open(pdf_path, 'rb') as f:
                file_content = await f.read()

            # 准备表单数据
            form_data = aiohttp.FormData()
            form_data.add_field('file', file_content,
                                filename=os.path.basename(pdf_path),
                                content_type='application/octet-stream')

            # 发送请求
            async with session.post(f"{self.base_url}/process_pdf/", data=form_data) as response:
                if response.status != 200:
                    raise Exception(f"处理失败，状态码: {response.status}, 响应: {await response.text()}")

                return await response.json()

    async def get_job_files(self, job_id: str) -> Dict[str, Any]:
        """
        异步获取指定任务的所有文件
        :param job_id: 任务ID
        :return: 文件列表
        """
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.base_url}/jobs/{job_id}") as response:
                if response.status != 200:
                    raise Exception(f"获取任务文件失败，状态码: {response.status}, 响应: {await response.text()}")

                return await response.json()

    async def download_file(self, file_url: str, save_path: str) -> str:
        """
        异步下载单个文件
        :param file_url: 文件URL路径（相对路径，如/download/job_id/filename）
        :param save_path: 文件保存路径
        :return: 保存后的文件完整路径
        """
        import aiohttp
        import aiofiles
        import os

        # 确保URL以/开头，且不以/结尾
        if not file_url.startswith('/'):
            file_url = '/' + file_url
        if file_url.endswith('/'):
            file_url = file_url[:-1]

        # 确保URL使用正斜杠而非反斜杠
        file_url = file_url.replace('\\', '/')

        # 构建完整URL
        full_url = f"{self.base_url}{file_url}"

        print(f"Downloading from URL: {full_url}")

        # 确保目标目录存在
        os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)

        # 发送异步请求下载文件
        async with aiohttp.ClientSession() as session:
            async with session.get(full_url) as response:
                if response.status != 200:
                    raise Exception(f"下载文件失败，状态码: {response.status}, 响应: {await response.text()}")

                # 异步保存文件
                async with aiofiles.open(save_path, 'wb') as f:
                    # 读取并写入数据块
                    while True:
                        chunk = await response.content.read(8192)
                        if not chunk:
                            break
                        await f.write(chunk)

        return os.path.abspath(save_path)

    async def download_all_files(self, job_id: str, output_dir: str) -> List[str]:
        """
        异步下载任务的所有文件
        :param job_id: 任务ID
        :param output_dir: 输出目录
        :return: 下载的文件路径列表
        """
        import os
        import json
        import asyncio

        # 获取任务文件列表
        job_data = await self.get_job_files(job_id)
        print(f"获取到任务文件: {json.dumps(job_data.get('files', []), indent=2, ensure_ascii=False)}")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        downloaded_files = []
        failed_files = []
        download_tasks = []

        # 准备所有下载任务
        for file_url in job_data.get('files', []):
            # 从URL中提取文件名
            parts = file_url.replace('\\', '/').split('/')
            filename = parts[-1]

            # 如果URL包含特定子目录，则创建对应的本地子目录结构
            sub_path = ""
            for part in parts:
                if part and part != "download" and part != job_id and part != filename:
                    sub_path = os.path.join(sub_path, part)

            if sub_path:
                save_dir = os.path.join(output_dir, sub_path)
                os.makedirs(save_dir, exist_ok=True)
                save_path = os.path.join(save_dir, filename)
            else:
                save_path = os.path.join(output_dir, filename)

            # 添加下载任务
            download_tasks.append((file_url, save_path))

        # 并发下载所有文件
        async def download_file_task(file_url, save_path):
            try:
                print(f"下载文件: {file_url} 到 {save_path}")
                downloaded_file = await self.download_file(file_url, save_path)
                return (True, downloaded_file, file_url)
            except Exception as e:
                print(f"❌ 下载文件失败 {file_url}: {str(e)}")
                return (False, None, file_url)

        # 创建并执行所有下载任务
        tasks = [download_file_task(url, path) for url, path in download_tasks]
        results = await asyncio.gather(*tasks)

        # 处理下载结果
        for success, file_path, file_url in results:
            if success:
                downloaded_files.append(file_path)
            else:
                failed_files.append(file_url)

        # 打印下载结果
        if failed_files:
            print(f"下载完成，成功: {len(downloaded_files)}，失败: {len(failed_files)}")
            print("失败的文件:")
            for f in failed_files:
                print(f" - {f}")
        else:
            print(f"所有文件下载成功，共 {len(downloaded_files)} 个文件")

        return downloaded_files

    def check_service_health(self) -> bool:
        """
        检查服务健康状态
        :return: 服务是否健康运行
        """
        try:
            response = requests.get(f"{self.base_url}/")
            return response.status_code == 200 and response.json().get('status') == 'OK'
        except Exception:
            return False
