import asyncio
import os

import aiohttp
import requests
from docling.document_converter import DocumentConverter
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from core.knowledge_base.front_upload.document_parser import parse_document
from core.knowledge_base.vector_db import MilvusTools

converter = DocumentConverter()
from pymilvus import MilvusClient, DataType


def create_collection(client, collection_name):
    schema = MilvusClient.create_schema(
        enable_dynamic_field=True,
    )

    schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True)
    schema.add_field(field_name="file_name", datatype=DataType.VARCHAR, max_length=256)
    schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=4096)
    schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=1024)
    schema.add_field(field_name="timestamp", datatype=DataType.INT64)
    schema.add_field(field_name="category", datatype=DataType.VARCHAR, max_length=128)
    schema.add_field(field_name="file_id", datatype=DataType.VARCHAR, max_length=255)

    # 设置索引参数
    index_params = client.prepare_index_params()

    # 设置主键索引
    index_params.add_index(
        field_name="id",
        index_type="STL_SORT"
    )
    # 设置向量索引
    index_params.add_index(
        field_name="vector",
        index_type="AUTOINDEX",
        metric_type="COSINE"
    )

    client.create_collection(
        collection_name=collection_name,
        schema=schema,
        index_params=index_params
    )


async def paras_file(file_path: str, kb_id: str, file_id: int, file_name: str, domain: str):
    def clean_markdown_table(markdown_text):
        lines = markdown_text.split('\n')
        # 去掉每列的多余空格，只保留必要的分隔符
        cleaned_lines = [('|'.join(col.strip() for col in line.split('|'))).strip() for line in lines]
        return '\n'.join(cleaned_lines)

    # 使用异步方式解析文档
    markdown_text = await parse_document(file_path)

    text_splitter = RecursiveCharacterTextSplitter(separators=["\n\n", "\n", " ", "", "####"],
                                                   chunk_size=512,
                                                   chunk_overlap=64)

    markdown_text = clean_markdown_table(markdown_text).replace("-", "")

    documents = Document(page_content=markdown_text, metadata={"source": file_name})
    chunks = text_splitter.split_documents([documents])

    # 入库
    client = MilvusClient(uri="http://**********:19530", token="root:Milvus", db_name="gc_knowledge")
    collections_list = client.list_collections()
    if kb_id not in collections_list:
        create_collection(client, kb_id)

    milvus_tool = MilvusTools(collection_name=kb_id)
    for idx, chk in enumerate(chunks):
        meta_data = chk.metadata
        text = chk.page_content
        res = milvus_tool.insert(text, meta_data, domain, file_id)
        print(res)

    return markdown_text


def write_file(path, content):
    with open(path, "wb") as f:
        f.write(content)


async def download_file(file_url: str):
    # 读取文件
    save_dir = os.path.join("cache", "user_upload")
    os.makedirs(save_dir, exist_ok=True)
    file_name = os.path.basename(file_url)
    save_path = os.path.join(save_dir, file_name)

    # 使用aiohttp进行异步下载
    async with aiohttp.ClientSession() as session:
        async with session.get(file_url) as response:
            if response.status == 200:
                content = await response.read()
                # 使用异步IO写入文件
                await asyncio.to_thread(write_file, save_path, content)
            else:
                raise Exception(f"Failed to download file from {file_url}, status: {response.status}")
    return save_path


async def upload_file_to_vector_db(file_url, kb_id, file_id, file_name, domain):
    save_path = await download_file(file_url)
    markdown_text = await paras_file(save_path, kb_id, file_id, file_name, domain)
    return markdown_text
