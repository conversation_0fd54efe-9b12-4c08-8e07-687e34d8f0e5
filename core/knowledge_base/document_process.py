from typing import List

import requests
from langchain.schema import Document
from langchain_community.document_loaders import PyPDFLoader, UnstructuredWordDocumentLoader, UnstructuredMarkdownLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter

from core.config.logging import logger
from core.config.vector_config import public_knowledge_collection
from core.knowledge_base.custom_parsers.pdf_parser import CustomPdfLoader
from core.knowledge_base.vector_db import MilvusTools


class DocumentProcessor:
    """
    文档处理类：用于处理文档的加载、分割、向量化和存储

    属性:
        embedding_model: 用于向量化的嵌入模型
        vector_store: 向量存储实例
        text_splitter: 文本分割器实例
    """

    def __init__(self, collection_name=public_knowledge_collection):
        """
        初始化文档处理器

        参数:
            embedding_model: 嵌入模型实例（默认使用OpenAIEmbeddings）
            vector_store_path: 向量数据库存储路径
        """
        self.milvus = MilvusTools(collection_name=collection_name)
        # self.rerank_url = "http://172.27.9.4:9997/v1/rerank"
        self.rerank_url = None
        self.rerank_model_name = "bge-reranker-large"

    def xinference_rerank(self, corpus, query):
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        data = {
            "model": self.rerank_model_name,
            "query": query,
            "documents": corpus
        }
        try:
            response = requests.post(self.rerank_url, json=data, headers=headers)
            response.raise_for_status()  # 检查是否请求成功
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Rerank服务请求失败: {e}")
            return None

    def _create_document_loader(self, file_path: str, **kwargs):
        """
        创建文档加载器实例

        参数:
            file_path: 文档文件路径
            kwargs: 加载器的额外参数

        返回:
            适当的文档加载器实例
        """
        suffix = file_path.split(".")[-1].lower()

        if suffix == "pdf":
            # return CustomPdfLoader(file_path)
            return PyPDFLoader(file_path, mode="single")

        elif suffix == "md":
            return UnstructuredMarkdownLoader(file_path)

        elif suffix in ["doc", "docx"]:
            mode = kwargs.get("mode", "elements")
            strategy = kwargs.get("strategy", "fast")
            return UnstructuredWordDocumentLoader(
                file_path,
                mode=mode,
                strategy=strategy
            )

        elif suffix == "xlsx":
            # return UnstructuredExcelLoader(
            #     file_path,
            #     mode="single"
            # )
            return DocumentConverter()


        elif suffix in ["ppt", "pptx"]:
            return CustomPdfLoader(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {suffix}")

    def load_document(self, file_path: str, **loader_kwargs) -> List[Document]:
        """
        从指定路径加载文档

        参数:
            file_path: 文档文件路径
            loader_kwargs: 传递给加载器的额外参数

        返回:
            加载的文档页面列表
        """
        try:
            loader = self._create_document_loader(file_path, **loader_kwargs)

            pages = []
            for page in loader.lazy_load():
                pages.append(page)

            logger.info(f"Successfully loaded {len(pages)} pages from {file_path}")
            return pages

        except Exception as e:
            logger.error(f"Error loading document {file_path}: {str(e)}")
            raise

    def split_documents(self,
                        documents: List[Document],
                        chunk_size: int = 1024,
                        chunk_overlap: int = 256) -> List[Document]:
        """
        将文档切分成小块进行处理

        参数:
            documents: 要切分的文档列表
            chunk_size: 每个文本块的大小
            chunk_overlap: 文本块之间的重叠字符数

        返回:
            切分后的Document对象列表
        """
        # text_splitter = ChineseRecursiveTextSplitter(
        #     keep_separator=True, is_separator_regex=True, chunk_size=512, chunk_overlap=128
        # )

        text_splitter = RecursiveCharacterTextSplitter(separators=["\n\n", "\n", " ", "", "####"],
                                                       chunk_size=chunk_size,
                                                       chunk_overlap=chunk_overlap)

        chunks = text_splitter.split_documents(documents)

        return chunks

    def vectorize_and_store(self, documents: List[Document], category="str") -> None:
        """
        将文档向量化并存储到向量数据库中

        参数:
            documents: 要向量化和存储的文档列表
        """
        logger.info(f"Starting vectorization of {len(documents)} documents")
        chucks = self.split_documents(documents, chunk_size=512, chunk_overlap=64)
        logger.info(f"Split into {len(chucks)} chunks")

        # tmp = '|制程|失效模式|失效原因|判定依据（检查内容）|纠正措施|预防措施|\n|--------------|--------------|--------------|--------------|--------------|--------------|\n'
        for idx, chk in enumerate(chucks):
            meta_data = chk.metadata
            text = chk.page_content
            # if idx >= 1:
            #     text = tmp + text
            res = self.milvus.insert(text, meta_data, category)

        logger.info(f"Completed vectorization and storage of {len(documents)} documents")

    def search_similar_documents(self,
                                 query: str,
                                 k: int = 20,
                                 rerank_k: int = 10,
                                 category=None) -> List[dict]:
        """
        在向量存储中搜索相似文档

        参数:
            query: 搜索查询字符串
            k: 返回结果的数量

        返回:
            相似文档列表
            [{'id': 456209097653228044, 'distance': 0.664517879486084, 'entity': {'id': 456209097653228044, 'text': ''}}]
        """
        if category is None:
            category = []

        if not category:
            logger.info("用户没有权限，无法搜索知识库")
            return []

        logger.info(f"Searching for similar documents with query: '{query[:50]}...' (top {k})")
        out = []
        similar_docs = self.milvus.search(query, k, category)
        for hits in similar_docs:
            for hit in hits:
                out.append(hit)

        # 应用重排逻辑
        if self.rerank_url is not None and len(out) > 1:
            try:
                logger.info("Applying reranking logic")

                # 提取文本内容列表用于重排
                corpus = [hit['entity'].get('text', '') for hit in out if 'entity' in hit and 'text' in hit['entity']]

                # 确保有足够的文本进行重排
                if len(corpus) > 1:
                    # 调用xinference的rerank API
                    rerank_results = self.xinference_rerank(corpus, query)["results"]

                    # 获取重排后的索引顺序
                    reranked_indices = [res['index'] for res in rerank_results[:rerank_k]]

                    # 根据重排结果重新排序原始输出
                    reranked_out = []
                    for idx in reranked_indices:
                        reranked_out.append(out[idx])

                    # 用重排后的结果替换原始结果
                    if reranked_out:
                        out = reranked_out
                        logger.info(f"Reranking completed successfully, reordered {len(out)} documents")
                    else:
                        logger.warning("Reranking did not produce valid results, using original ranking")
                else:
                    logger.warning("Not enough valid documents for reranking, using original ranking")
            except Exception as e:
                logger.error(f"Error during reranking: {str(e)}")
                logger.info("Using original vector search results due to reranking error")

        logger.info(f"Found {len(out)} similar documents")
        return out


if __name__ == "__main__":
    p = DocumentProcessor()

    # for file in ["去厚片机保养作业指导书.md", "创昱达CYD600G脱插洗一体机保养作业指导书.md"]:
    #     docs = p.load_document(rf"C:\Users\<USER>\PycharmProjects\GC-ES-LLM\dataset\分类数据\\脱插洗\{file}")
    #     p.vectorize_and_store(docs, category="TCX")

    from langchain.schema import Document
    from docling.document_converter import DocumentConverter


    def clean_markdown_table(markdown_text):
        lines = markdown_text.split('\n')
        # 去掉每列的多余空格，只保留必要的分隔符
        cleaned_lines = [('|'.join(col.strip() for col in line.split('|'))).strip() for line in lines]
        return '\n'.join(cleaned_lines)

    # root_path = r"C:\Users\<USER>\Desktop\1\2"
    # for file in os.listdir(root_path):
    #     if file.endswith(".md"):
    #         source = rf"C:\Users\<USER>\Desktop\1\2\{file}" # PDF path or URL

    # source = r"C:\Users\<USER>\PycharmProjects\GC-ES-LLM\core\knowledge_base\大循环异常处理.xlsx"
    # converter = DocumentConverter()
    # result = converter.convert(source)
    # markdown_text = result.document.export_to_markdown()
    # markdown_text = clean_markdown_table(markdown_text).replace("-", "")

    # documents = Document(page_content=markdown_text, metadata={"source": source})

    # 使用TextLoader加载文本文件
    #         loader = TextLoader(source, encoding="utf-8")
    #         documents = loader.load()

    # p.vectorize_and_store([documents], category="YC")

    content = p.search_similar_documents("切片机构造组成")
    # for c in content:
    #     print(c)

    print("Done")