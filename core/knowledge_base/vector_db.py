import os.path
from datetime import datetime

import time
from datetime import timedelta

from langchain_openai import OpenAIEmbeddings
from pymilvus import MilvusClient

from core.config.logging import logger
from core.config.vector_config import embedding_model_name, embedding_model_api_base, milvus_client_url, milvus_db_name, \
    vector_search_type


class MilvusTools:
    def __init__(self, collection_name):
        total_start_time = time.time()
        start_time = time.time()
        self.client = MilvusClient(uri=milvus_client_url, token="root:Milvus", db_name=milvus_db_name)
        elapsed = timedelta(seconds=time.time() - start_time)
        self.collection_name = collection_name
        self.embedding_model = OpenAIEmbeddings(model=embedding_model_name,
                                                openai_api_base=embedding_model_api_base,
                                                openai_api_key="...")

        kb_name = collection_name
        kb_type = 'milvus'  # 或根据实际情况从 milvus 获取
        vector_model = embedding_model_name  # 可从配置或参数获取
        total_files = self._get_file_num()
        total_entries = self.client.get_collection_stats(collection_name)["row_count"]

        self.collection_name = collection_name

        logger.info("------------------------------------------------------------------------------------")
        logger.info(f"知识库名称      ：{kb_name}")
        logger.info(f"知识库类型      ：{kb_type}")
        logger.info(f"向量模型        ：{vector_model}")
        logger.info(f"文件总数量      ：{total_files}")
        logger.info(f"知识条目数      ：{total_entries}")
        logger.info(f"用时            ：{elapsed}")
        logger.info("------------------------------------------------------------------------------------")

        total_elapsed = timedelta(seconds=time.time() - total_start_time)
        logger.info(f"总计用时        ：{total_elapsed}")

    def _get_file_num(self):
        iterator = self.client.query_iterator(
            collection_name=self.collection_name,  # 使用从前端传递的参数
            filter="",
            output_fields=["file_name", "category"],
            batch_size=100
        )

        grouped_files = {}  # 存储按类别分组的文件
        duplicate = set()

        while True:
            res = iterator.next()
            if len(res) == 0:
                iterator.close()
                break

            for item in res:
                file_name = item["file_name"]
                category = item["category"]

                if file_name not in duplicate:
                    if category not in grouped_files:
                        grouped_files[category] = []
                    grouped_files[category].append(file_name)
                    duplicate.add(file_name)
        return len(duplicate)

    def insert(self, text: str, meta_data: dict = None, category: str = None, file_id: int = None,
               context_content: str = None):
        """
        插入文本向量到向量数据库中
        """
        file_name = os.path.basename(meta_data["source"])
        logger.info(f"Inserting vector for file: {file_name}")
        vector = self._encode(text)

        # 数据模板
        if context_content:
            data = {
                "file_name": file_name,
                "child_text": text,
                "parent_text": context_content,
                "vector": vector,
                "timestamp": int(datetime.now().timestamp()),
                "category": category,
                "file_id": str(file_id)
            }
        else:
            data = {
                "file_name": file_name,
                "text": text,
                "vector": vector,
                "timestamp": int(datetime.now().timestamp()),
                "category": category,
                "file_id": str(file_id)
            }

        res = self.client.insert(collection_name=self.collection_name, data=[data])

        return res

    def _encode(self, text):
        """
        将文本转换为向量
        :param text:
        :return:
        """
        return self.embedding_model.embed_query(text)

    def search(self, text, top_k, category: list):
        """
        查找相似的向量文本
        :param category: list[str]: ["red", "green", "blue"]
        :param text:
        :param top_k:
        :return:
        """
        _vector = self._encode(text)

        # 个人知识库默认搜索所有类别
        if "all" in category:
            category = []

        if vector_search_type == "normal":
            res = self.client.search(collection_name=self.collection_name,
                                     anns_field="vector",
                                     data=[_vector],
                                     limit=top_k,
                                     params={"metric_type": "L2"},
                                     output_fields=["id", "text", "file_name"],
                                     filter=f'category in {category}' if category else '')
        elif vector_search_type == "parent":
            res = self.client.search(collection_name=self.collection_name,
                                     anns_field="vector",
                                     data=[_vector],
                                     limit=top_k,
                                     params={"metric_type": "L2"},
                                     output_fields=["id", "parent_text", "file_name"],
                                     filter=f'category in {category}' if category else '')
        else:
            raise Exception("vector_search_type must be normal or parent")
        return res
