from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from core.config.vector_config import vector_search_type
from core.knowledge_base.vector_db import MilvusTools


def store_document_chunks(text, source, category="TT", collection_name="knowledge_20250303", context_window=3):
    """
    将文档切块并存入向量数据库

    Args:
        text: markdown格式的文本
        source: 文件来源
        category: 类别，默认为"TT"
        collection_name: 集合名称，默认为"knowledge_20250303"
        context_window: 上下文窗口

    Returns:
        处理结果

    """
    milvus_tool = MilvusTools(collection_name=collection_name)

    # 文本分割器
    text_splitter = RecursiveCharacterTextSplitter(
        separators=["\n\n", "\n", " ", "", "####"],
        chunk_size=512,
        chunk_overlap=64
    )

    # 创建文档对象并分块
    documents = Document(page_content=text, metadata={"source": source})
    chunks = text_splitter.split_documents([documents])

    # 将文档块插入Milvus

    results = []

    for idx, chunk in enumerate(chunks):
        meta_data = chunk.metadata
        chunk_text = chunk.page_content
        if vector_search_type == "parent":
            # 获取上下文窗口范围内的内容
            start = max(0, idx - context_window)
            end = min(len(chunks), idx + context_window + 1)
            context_content = ''.join([chunks[i].page_content for i in range(start, end)])
            res = milvus_tool.insert(chunk_text, meta_data, category, context_content=context_content)

        elif vector_search_type == "normal":
            res = milvus_tool.insert(chunk_text, meta_data, category)

        else:
            raise ValueError(f"Unknown vector_search_type: {vector_search_type}")

        results.append(res)

    return {
        "total_chunks": len(chunks),
        "results": results
    }