import hashlib
import re
import shutil
import time
import os

from docling.document_converter import DocumentConverter

from core.knowledge_base.custom_parsers.excel_parser import RAGFlowExcelParser
from core.knowledge_base.custom_parsers.pdf_parser import PDFProcessingClient, AsyncPDFProcessingClient

converter = DocumentConverter()
excel_parser = RAGFlowExcelParser()


def replace_image_prefix_in_file(md_file_path):
    """替换Markdown文件中的图片前缀"""
    with open(md_file_path, 'r', encoding='utf-8') as file:
        md_content = file.read()

    # 正则表达式匹配 ![](images/<image_hash>.jpg)
    pattern = r'!\[\]\(images/([a-f0-9]+\.jpg)\)'

    # 替换图片路径前缀
    modified_content = re.sub(pattern, r'![图片链接](http://172.27.9.4:59990/static/\1)', md_content)

    # 保存修改后的内容
    with open(md_file_path, 'w', encoding='utf-8') as file:
        file.write(modified_content)

    print(f"文件 {md_file_path} 已成功更新。")


def replace_circled_numbers(md_file_path):
    """
    替换Markdown文件中的 $\textcircled{x}$
    :param md_file_path:
    :return:
    """
    # 读取文件内容
    with open(md_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # 使用正则表达式匹配 $\textcircled{x}$ 格式
    # 其中 x 是一个数字
    updated_content = re.sub(r'\\textcircled{(\d)}', r'\1', content)

    # 将替换后的内容写回文件
    with open(md_file_path, 'w', encoding='utf-8') as file:
        file.write(updated_content)

    print("文件处理完成！")


def generate_short_id():
    """
    生成一个8位的短ID，基于当前时间戳的哈希值。
    """
    # 获取当前时间戳
    timestamp = str(time.time())

    # 计算哈希值
    hash_object = hashlib.sha1(timestamp.encode())

    # 获取前 8 位作为短标识符
    short_id = hash_object.hexdigest()[:8]

    return short_id


def process_md_file(md_file_path, image_path):
    """
    处理Markdown文件，替换其中图片的路径为短标识符并修改文件名。

    :param md_file_path: Markdown文件路径
    :param image_path: 图片文件夹路径
    """
    # 读取Markdown文件内容
    with open(md_file_path, 'r', encoding='utf-8') as file:
        md_content = file.read()

    # 匹配Markdown中的图片链接
    image_pattern = r'!\[图片链接]\((http://172\.27\.9\.4:59990/static/)([a-fA-F0-9]+\.jpg)\)'
    matches = re.findall(image_pattern, md_content)

    # 遍历所有匹配的图片链接并修改文件名
    for match in matches:
        old_image_url = match[0] + match[1]
        old_image_filename = match[1]

        # 生成新的短ID
        new_short_id = generate_short_id()

        # 计算新图片文件名
        new_image_filename = f'{new_short_id}.jpg'
        new_image_url = match[0] + new_image_filename

        # 修改Markdown文件中的图片链接
        md_content = md_content.replace(old_image_url, new_image_url)

        # 移动并重命名图片文件
        old_image_file_path = os.path.join(image_path, old_image_filename)
        new_image_file_path = os.path.join(image_path, new_image_filename)

        # 确保图片文件存在
        if os.path.exists(old_image_file_path):
            shutil.move(old_image_file_path, new_image_file_path)

    # 将修改后的内容写回Markdown文件
    with open(md_file_path, 'w', encoding='utf-8') as file:
        file.write(md_content)


def clean_markdown_table(markdown_text):
    """清理Markdown表格格式，去除多余空格"""
    lines = markdown_text.split('\n')
    # 去掉每列的多余空格，只保留必要的分隔符
    cleaned_lines = [('|'.join(col.strip() for col in line.split('|'))).strip() for line in lines]
    return '\n'.join(cleaned_lines)


def format_md_text(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        md_text = file.read()
    # 去除多余的换行符，连续多个换行符保留一个
    md_text = re.sub(r'\n+', '\n', md_text)
    # 去除行尾和行首的空格
    md_text = re.sub(r'^\s+|\s+$', '', md_text)
    # 规范化段落之间的空行（例如，删除过多的空行）
    md_text = re.sub(r'(\n\s*\n)+', '\n\n', md_text)
    return md_text


async def parse_document(file_path: str):
    """
    解析文档，将文档转换为markdown格式

    Args:
        file_path: 文件路径

    Returns:
        str: markdown格式的文本
    """
    # doc和docx先转换为pdf
    # if file_path.endswith(".doc") or file_path.endswith(".docx"):
    #    pass

    if file_path.endswith(".pdf"):# or file_path.endswith(".doc") or file_path.endswith(".docx"):
        # loader = PDFProcessingClient()
        loader = AsyncPDFProcessingClient()
        # 上传任务
        result = await loader.process_pdf(file_path)
        job_id = result['job_id']
        output_dir = f"cache/process_file_temp/downloaded_files_{job_id}"
        # 下载文件
        downloaded_files = await loader.download_all_files(job_id, output_dir)

        # 替换图片前缀
        md_file_path = ""
        for file in downloaded_files:
            if file.endswith('.md'):
                replace_image_prefix_in_file(file)
                md_file_path = file
                print(f"图片前缀已成功替换在文件: {file}")

        # 移动图片到/static目录
        for file in downloaded_files:
            if file.endswith('.jpg'):
                new_path = os.path.join("static", os.path.basename(file))
                os.rename(file, new_path)
                print(f"图片已成功移动到: {new_path}")

        # 缩短图片文件名
        process_md_file(md_file_path, "./static")

        # 替换$\textcircled{x}$
        replace_circled_numbers(md_file_path)

        # 清理多余的换行符
        return format_md_text(md_file_path)

    elif file_path.endswith(".txt"):
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()

    elif file_path.endswith(".xlsx") or file_path.endswith(".xls"):
        with open(file_path, 'rb') as f:
            row_data = excel_parser(f.read())
            return "\n\n".join(row_data)

    else:
        # 使用converter将文件转换为文档对象
        doc = converter.convert(source=file_path).document

        # 导出为markdown文本
        markdown_text = doc.export_to_markdown(delim="\n", image_placeholder="")

        # 清理markdown表格和特殊字符
        clean_text = clean_markdown_table(markdown_text).replace("-", "")

        return clean_text


# if __name__ == '__main__':
#     import os
#
#     os.chdir(r"C:\Users\<USER>\PycharmProjects\GC-ES-LLM")
#     file_path = r"C:\Users\<USER>\Desktop\工作\大模型项目\知识收集\实验室仪器设备操作规程\硅酸根测试方法-ue.doc"
#     res = parse_document(file_path)
#     print(res)
