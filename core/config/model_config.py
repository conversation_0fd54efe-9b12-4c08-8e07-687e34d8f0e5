import openai

xinference_client = openai.Client(api_key="not used actually", base_url="http://172.27.9.4:9100/v1")
think_model_name = "Qwen3-32B-AWQ"
think_model_temperature = 0.6
think_model_top_p = 0.95

xinference_client2 = openai.Client(api_key="not used actually", base_url="http://172.27.9.4:9100/v1")
no_think_model_name = "Qwen3-32B-AWQ"
no_think_model_temperature = 0.7
no_think_model_top_p = 0.8

presence_penalty = 1.5
