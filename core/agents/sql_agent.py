# Copyright 2023 The Qwen team, Alibaba Group. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""An agent implemented by assistant with qwen3"""
import os  # noqa
from uuid import uuid4

import json5
import requests
from qwen_agent.agents import Assistant
from qwen_agent.tools.base import register_tool, BaseTool
from qwen_agent.utils.output_beautify import typewriter_print


@register_tool('query_data')
class QueryData(BaseTool):
    # `description` 用于告诉智能体该工具的功能。
    description = 'postgreSQL数据库查询服务，输入文本描述，返回对应数据'
    # `parameters` 告诉智能体该工具有哪些输入参数。
    parameters = [{
        'name': 'query',
        'type': 'string',
        'description': '期望查询的数据',
        'required': True
    }]

    def call(self, query: str, **kwargs) -> str:
        # `params` 是由 LLM 智能体生成的参数。
        query = json5.loads(query)['query']
        url = "http://**********:59990/api/chat"
        uuid = uuid4()
        body = {
            "userId": "D0024",
            "uuid": str(uuid),
            "messages": query,
            "deepSeek": False,
            "person": False,
            "knowledge": False,
            "sql": True,
            "dataBI": False,
            "dify_agv": False,
            "dify_slot_recommend": False
        }
        response = requests.post(url, json=body).json()
        data = response.get("table_element")

        return data


llm_cfg = {
    # Use your own model service compatible with OpenAI API by vLLM/SGLang:
    'model': 'Qwen3-32B-AWQ',
    'model_server': 'http://**********:9100/v1',  # api_base
    'api_key': 'EMPTY',

    'generate_cfg': {
        # When using vLLM/SGLang OAI API, pass the parameter of whether to enable thinking mode in this way
        'extra_body': {
            'chat_template_kwargs': {'enable_thinking': False}
        },

        # Add: When the content is `<think>this is the thought</think>this is the answer`
        # Do not add: When the response has been separated by reasoning_content and content
        # This parameter will affect the parsing strategy of tool call
        # 'thought_in_content': False,
    },
}
tools = ['query_data', 'code_interpreter']
bot = Assistant(llm=llm_cfg,
                function_list=tools,
                name='数据查询助手',
                description="查询实验车间生产数据",
                system_message="")

# # 步骤 4：作为聊天机器人运行智能体。
messages = [{'role': 'user', 'content': "查询FQ01良率情况"}]
response_plain_text = ''
for response in bot.run(messages=messages):
    response_plain_text = typewriter_print(response, response_plain_text)

# from qwen_agent.gui import WebUI
# def app_gui():
#     # Define the agent
#     chatbot_config = {
#         'prompt.suggestions': [
#             'What time is it?',
#             'https://github.com/orgs/QwenLM/repositories Extract markdown content of this page, then draw a bar chart to display the number of stars.'
#         ]
#     }
#     WebUI(
#         bot,
#         chatbot_config=chatbot_config,
#     ).run()
#
# if __name__ == '__main__':
#     # test()
#     # app_tui()
#     app_gui()