from abc import ABC, abstractmethod

from api.schemas.chat import QueryRequest
from api.utils import get_user_roles_by_user_id


class BaseAgents(ABC):
    def __init__(self):
        pass

    @abstractmethod
    async def chat(self, request_data: QueryRequest):
        """
        非流式问答功能
        """
        pass

    @abstractmethod
    async def chat_stream(self, query, history):
        """
        流式问答功能，yield/async generator实现
        """
        pass


class DemoAgent(BaseAgents):
    async def chat(self, request_data: QueryRequest):
        # 非流式实现
        result = ...
        return result

    async def chat_stream(self, request_data: QueryRequest):
        func = ...
        # 流式实现
        async for chunk in func:
            yield chunk