import json

import requests

from api.schemas.chat import QueryRequest
from core.agents.base_agent import BaseAgents


class MeetingMinutesAgent(BaseAgents):
    async def chat(self, request_data: QueryRequest):
        pass

    def __init__(self):
        super().__init__()

    async def chat_stream(self, query, history, filter_str=""):
        """
        外层业务处理，按需处理 request_data 字段
        """

        # 其他业务逻辑、权限校验等
        # ...

        # 只将需要的参数（如 query）传给核心流式接口
        api_payload = {
            "query": query,
            # 可根据接口需要填充/调整其余字段
            "mode": "hybrid",
            "only_need_context": False,
            "only_need_prompt": False,
            "response_type": "Multiple Paragraphs",
            "stream": True,
            "top_k": 10,
            "max_token_for_text_unit": 4000,
            "max_token_for_global_context": 4000,
            "max_token_for_local_context": 4000,
            "conversation_history": history,
            "history_turns": 3,
            "user_prompt": "",
            "filter_str": filter_str
        }

        # 可以根据业务做动态调整
        # api_payload["user_prompt"] = request_data.userPrompt or ""

        # 核心流式请求
        for chunk in self._query_lightrag(api_payload):
            yield chunk

    @staticmethod
    def _query_lightrag(api_payload: dict):
        """
        封装流式接口请求，优化结构与异常处理
        """
        url = "http://**********:9632/query/stream"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(
                url, headers=headers, json=api_payload, stream=True, timeout=60
            )
            response.raise_for_status()

            for raw_chunk in response.iter_lines():
                if not raw_chunk:
                    continue
                try:
                    # 解码并尝试json解析
                    chunk = raw_chunk.decode("utf-8")
                    data_json = json.loads(chunk)
                    msg = data_json.get("response") or data_json.get("msg") or ""
                except Exception:
                    msg = chunk if 'chunk' in locals() else raw_chunk

                sse_data = {
                    "type": "text",
                    "msg": msg if isinstance(msg, str) else str(msg)
                }
                yield f"data: {json.dumps(sse_data, ensure_ascii=False)}\n\n".encode("utf-8")

            # 结尾done
            done_data = {"type": "Done", "msg": ""}
            yield f"data: {json.dumps(done_data, ensure_ascii=False)}\n\n".encode('utf-8')

        except requests.exceptions.HTTPError as http_err:
            if response is not None and response.status_code == 422:
                try:
                    error_detail = response.json().get("detail", [])
                except Exception:
                    error_detail = response.text
                yield f"[ERROR] 验证错误: {error_detail}".encode("utf-8")
            else:
                yield f"[ERROR] HTTP异常: {http_err}".encode("utf-8")
        except Exception as e:
            yield f"[ERROR] 请求失败: {str(e)}".encode("utf-8")


if __name__ == '__main__':
    import asyncio
    agent = MeetingMinutesAgent()

    async def main():
        async for chunk in agent.chat_stream("研发中心最近有哪些会议", history=[]):
            print(chunk.decode("utf-8"))

    asyncio.run(main())