import os  # noqa
from uuid import uuid4

import json5
import requests
from qwen_agent.tools.base import register_tool, BaseTool


# 步骤 1（可选）：添加一个名为 `my_image_gen` 的自定义工具。
@register_tool('query_data')
class QueryData(BaseTool):
    # `description` 用于告诉智能体该工具的功能。
    description = 'postgreSQL数据库查询服务，输入文本描述，返回对应数据'
    # `parameters` 告诉智能体该工具有哪些输入参数。
    parameters = [{
        'name': 'query',
        'type': 'string',
        'description': '期望查询的数据',
        'required': True
    }]

    def call(self, query: str, **kwargs) -> str:
        # `params` 是由 LLM 智能体生成的参数。
        query = json5.loads(query)['query']
        url = "http://**********:59990/api/chat"
        uuid = uuid4()
        body = {
              "userId": "D0024",
              "uuid": str(uuid),
              "messages": query,
              "deepSeek": False,
              "person": False,
              "knowledge": False,
              "sql": True,
              "dataBI": False,
              "dify_agv": False,
              "dify_slot_recommend": False
            }
        response = requests.post(url, json=body).json()
        data = response.get("table_element")

        return data