import argparse
import json
import openai
import psycopg2
import atexit
import os
import multiprocessing
import datetime
import re
import time
from typing import List, Dict, Any
import pandas as pd

from core.config.logging import logger
from core.text2sql.model import MyVannaWithCorrection


vn_with_correction = MyVannaWithCorrection()


def query_by_vanna(query):
    sql, df, error = vn_with_correction.ask_with_correction(
        query,
        auto_train=False,
        visualize=False,
        print_results=False,
        role=[{"name": "知识库-数据检索-管理员"}],
    )
    return sql, df, error


def generate_test_set(testset_file: str):
    test_set = []
    
    sql_data = vn_with_correction.sql_collection.get()
    if sql_data is not None:
        for index, doc in enumerate(sql_data.get("documents", [])):
            ids = sql_data.get("ids", [])
            doc = json.loads(doc)

            test_set.append(
                {
                    "question_id": ids[index],
                    "db_id": "",
                    "question": doc["question"],
                    "evidence": "",
                    "SQL": doc["sql"],
                    "difficulty": "challenging"
                }
            )
    with open(testset_file, 'w', encoding='utf-8') as f:
        json.dump(test_set, f, ensure_ascii=False, indent=2)
    logger.info(f"生成测试集: {len(test_set)} 条")
    return test_set


def execute_sql_by_postgresql(gold_sql, predicted_sql):
    """
    执行并比较两个SQL语句的结果，每次执行都创建新连接
    
    Args:
        gold_sql: 标准SQL语句
        predicted_sql: 预测的SQL语句
        
    Returns:
        bool: 两个SQL执行结果是否匹配
    """
    cursor = None
    conn = None
    max_retries = 3
    retry_count = 0
    
    db_config = {"host": "***********", "database": "gc_pds", "user": "readonly", "password": "YbReadOnly0712", "port": 5432}
    
    while retry_count < max_retries:
        try:
            # 创建新的连接
            conn = psycopg2.connect(
                host=db_config["host"],
                database=db_config["database"],
                user=db_config["user"],
                password=db_config["password"],
                port=db_config["port"]
            )
            cursor = conn.cursor()
            
            # 执行标准答案的SQL
            gold_sql_columns = None
            gold_sql_rows = None
            gold_sql_error = None
            try:
                cursor.execute(gold_sql)
                gold_sql_columns = [desc[0] for desc in cursor.description]
                gold_sql_rows = cursor.fetchall()
            except Exception as e:
                logger.error(f"执行标准答案的SQL时出错: {e}")
                gold_sql_error = str(e)
            
            # 执行预测的SQL
            predicted_sql_columns = None
            predicted_sql_rows = None
            predicted_sql_error = None
            try:
                cursor.execute(predicted_sql)
                predicted_sql_columns = [desc[0] for desc in cursor.description]
                predicted_sql_rows = cursor.fetchall()
            except Exception as e:
                logger.error(f"执行预测的SQL时出错: {e}")
                predicted_sql_error = str(e)
            
            # 列名比较
            columns_match = calculate_ex(gold_sql_columns, predicted_sql_columns)
            
            # 数据行比较
            rows_match = calculate_ex(gold_sql_rows, predicted_sql_rows)
            
            # 完全匹配需要列名和数据行都匹配
            result = columns_match and rows_match
            
            # 记录比较结果
            match_info = {
                "gold_sql_error": gold_sql_error,
                "predicted_sql_error": predicted_sql_error,
                "columns_match": columns_match,
                "rows_match": rows_match,
                "result": result,
            }
            
            logger.info(f"查询结果比较: {match_info}")
            return match_info
            
        except Exception as e:
            retry_count += 1
            logger.error(f"PostgreSQL执行错误 (尝试 {retry_count}/{max_retries}): {e}")
            logger.error(f"Gold SQL: {gold_sql[:300]}...")
            logger.error(f"Predicted SQL: {predicted_sql[:300]}...")
            
            # 最后一次重试仍然失败
            if retry_count >= max_retries:
                return False
            
            # 等待一段时间再重试
            time.sleep(1)  # 等待1秒后重试
            
        finally:
            # 每次执行完毕都关闭游标和连接
            if cursor:
                cursor.close()
            if conn:
                conn.close()


def calculate_ex(predicted_res, ground_truth_res):
    res = 0
    if predicted_res is None or ground_truth_res is None:
        return res
    if set(predicted_res) == set(ground_truth_res):
        res = 1
    return res


def clean_sql(sql):
    """
    清理SQL字符串，替换换行符、制表符和多余的空格
    
    Args:
        sql: 原始SQL字符串
    
    Returns:
        清理后的SQL字符串
    """
    if not sql:
        return sql
    
    sql = sql.replace("```sql", "").replace("```", "")
    sql = sql.replace("-- 小指标", "")
    sql = sql.replace("-- Break line statistics", "")
    
    # 先替换换行符和制表符为空格
    cleaned = sql.replace("\n", " ").replace("\t", " ")
    
    # 使用正则表达式替换连续的空格为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    # 去除首尾空格
    return cleaned.strip()


def process_nl2sql(input_file: str, output_file: str) -> None:
    """
    读取JSON列表文件，使用query_by_vanna处理每个问题，输出结果到新的JSON文件
    
    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
    """
    try:
        # 读取输入JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            queries = json.load(f)
        
        results = []
        # 遍历每个问题
        for query_item in queries:
            question_id = query_item.get('question_id')
            question = query_item.get('question')
            
            if not question:
                logger.warning(f"跳过ID {question_id}：问题为空")
                continue
            
            # 使用query_by_vanna处理问题
            sql, df, error = query_by_vanna(question)
            
            # 将DataFrame转换为列表格式
            result = None
            # if df is not None and not df.empty:
            #     # 处理特殊类型后转dict
            #     result = {
            #         "columns": df.columns.tolist(),
            #         "data": json.loads(df.to_json(orient='records', date_format='iso'))
            #     }
            
            # 将结果添加到结果列表
            results.append({
                "question_id": question_id,
                "question": question,
                "sql": clean_sql(sql) if sql else str(error),
                "result": result,
                "error": str(error)
            })
            
            logger.info(f"处理完成：问题ID {question_id}")
        
        # 将结果写入输出JSON文件
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理完成：共处理 {len(results)} 个问题，结果已保存到 {output_file}")
    
    except Exception as e:
        logger.error(f"处理JSON文件时出错：{e}")
        raise


def process_nl2sql_by_zero_shot(input_file: str, output_file: str) -> None:
    """
    使用零样本提示处理JSON列表文件，为每个问题生成SQL
    
    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
    """
    try:
        # 读取输入JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            queries = json.load(f)
        
        results = []
        # 遍历每个问题
        for query_item in queries:
            question_id = query_item.get('question_id')
            question = query_item.get('question')
            
            if not question:
                logger.warning(f"跳过ID {question_id}：问题为空")
                continue
            
            sql = None
            error = None
            try:
                # 使用新版OpenAI API格式
                from openai import OpenAI
                
                # 创建客户端实例
                client = OpenAI(
                    api_key="...",
                    base_url="http://10.26.66.2:9100/v1"
                )
                
                # 发送请求
                response = client.chat.completions.create(
                    model="qwen2.5-coder-32B-GPTQ-INT8",
                    messages=[
                    {"role": "system", "content": '''================ 请将以下自然语言查询转换为一个精确的PostgreSQL SQL语句。
    - 只输出一个完整的、可直接执行的SQL语句、sql语句输出在```sql```中
    - 不要包含任何解释或多个SQL版本
    - 确保SQL语法符合PostgreSQL标准
    - 如果查询条件或返回字段有Postgresql的预留字段，则需要加上引号
    - 用户提问中的时间请以数据库当前时间做参照
    - 查询条件中时间戳和字符串做比较时，需要做时间格式转换
    - 如果用户没有特殊说明，提供的参考SQL也没有排序，则按提供的默认排序方式返回。同时要确保排序字段在SQL中存在
    - 基于给定的表结构和历史对话信息生成查询，如果用户问题重复，请尽量参考历史对话信息，默认历史对话信息的sql语句都是正确的

    ================ Document：
    机台号和设备编号是一个概念

    ================ Postgre SQL表及其属性如下：
    【关联关系】
    实验车间良率指标数据表的slice_mch_no = 实验车间切片机生产配置表machine_code
    实验车间良率指标数据表的slice_mch_no = 实验车间断线记录表的device_no
    实验车间良率指标数据表的knife_sn = 实验车间断线记录表的cut_no
    实验车间断线记录表.设备编号(device_no) = 实验车间切片机生产配置表.设备编码(machine_code)

    ================ 【表结构】
    实验车间良率指标数据表(pds_all_round_report)(
    id SERIAL 主键,
    fx_workdate DATE 分选工作日,
    fx_datetime TIMESTAMP(6) 分选时间,
    client_name VARCHAR(50) 客户名称,
    orgsn VARCHAR(50) 随工单号,
    product_spec VARCHAR(50) 产品规格,
    product_code VARCHAR(50) 产品编码,
    stick_type VARCHAR(50) 粘棒线类别,
    lot_sn VARCHAR(200) 晶棒号,
    knife_sn VARCHAR(50) 切片刀号,
    varline_sn VARCHAR(50) 钢线编号,
    varline_cut_sort INT4 钢线切割刀次,
    wheel_slot_pitch VARCHAR(50) 主辊槽距,
    wheel_slot_code VARCHAR(100) 主辊编号,
    wheel_slot_sort INT2 主辊切割次数,
    slice_operator VARCHAR(50) 切片操作人,
    takeup_operator VARCHAR(50) 下机操作人,
    slice_mch_no VARCHAR(50) 切片机编号,
    fx_mchno VARCHAR(50) 分选机编号,
    slice_type VARCHAR(50) 切片类型研发测试or量产,
    slice_shift VARCHAR(50) 切片班次,
    process_code VARCHAR(100) 工艺编号,
    theoretical_amount INT4 理论片数,
    a_amount INT4 A片数,
    b_amount INT4 B片数,
    c_amount INT4 C片数,
    d_amount INT4 D片数,
    a0_amount INT4 A0汇总,
    kg_piece FLOAT4 工艺线耗,
    spec_size VARCHAR(50) 硅片尺寸,
    spec_thickness VARCHAR(50) 硅片标准片厚,
    avg_thinkiness FLOAT4 硅片平均片厚
    )

    【表说明】
    实验车间良率指标数据表：记录硅片生产过程中的切片工艺参数、分选结果、操作人员及设备信息等数据，用于良率分析与生产管理。

    查询实验车间良率指标数据表的`良率`时，需要返回字段包括：
    - A率 = a_amount/theoretical_amount
    - B率 = b_amount/theoretical_amount
    - AB率 = (a_amount+b_amount)/theoretical_amount
    - C率 = c_amount/theoretical_amount
    - D率 = d_amount/theoretical_amount
    - 正A率 = (a_amount-a0_amount)/theoretical_amount
    - 直通率 = (a_amount+b_amount+c_amount+d_amount)/theoretical_amount
    
    查询实验车间良率指标数据表的`小指标`时，需要返回字段包括：
    - A-线痕 = (a0_amount_linemarks+a1_amount_linemarks)/theoretical_amount
    - C厚度 = c_hd/theoretical_amount
    - A-色差 = a0_sc/theoretical_amount
    - 脏污率 = dirty_heavy_amount/theoretical_amount
    - 崩边率 = collapse_total/theoretical_amount
    - 直流率 = b_zl/theoretical_amount
    - 缺角率 = d_missangle/theoretical_amount
    - 隐裂率 = yl_amount/theoretical_amount
    - B其他率 = b_other/theoretical_amount
    - D其他率 = d_other/theoretical_amount

    查询实验车间良率指标数据表的`加切异常`时，查询判断条件： exception1 IS NOT NULL AND exception1 <> ''

    实验车间良率指标数据表的`加切率` = 加切异常数/总数

    查询实验车间良率指标数据表的`卡线异常`时，查询判断条件： exception2 IS NOT NULL AND exception2 <> ''
    
    实验车间良率指标数据表的`卡线率` = 卡线异常数/总数

    实验车间良率指标数据表的slice_type，取值包括但不限于以下：
    - 量产
    - 研发测试

    实验车间良率指标数据表的knife_sn，示例如下：
    - FQ02-20250402-1，对应含义：机台号-时间-今天切割顺序编号

    实验车间良率指标数据表的varline_sn，示例如下：
    - GC26XG250117F0261610，对应含义：前缀(GC)+钢线号(26)+钢线类型(XG)+日期(250117)+编号(F0261610)
    
    实验车间良率指标数据表的slice_mch_no，示例如下：
    - FQ02

    【默认排序方式】
    fx_workdate DESC, fx_datetime DESC, slice_mch_no ASC, varline_sn ASC

    ================【表结构】
    实验车间断线记录表(mes_model_break)(
    id SERIAL 主键,
    cut_no VARCHAR(32) 刀号,
    device_no VARCHAR(255) 设备编号,
    breaking_time TIMESTAMP 断线时间,
    depth VARCHAR(255) 断线深度,
    varline VARCHAR(255) 钢线编号,
    'desc' VARCHAR(255) 断线描述,
    break_reason VARCHAR(255) 断线原因,
    break_class VARCHAR(255) 断线类别,
    cir VARCHAR(255) 排查情况,
    measure VARCHAR(255) 改善措施,
    person VARCHAR(255) 责任人
    workdate VARCHAR(10) 工作日,
    break_loss VARCHAR(255) 断线损失,
    handler VARCHAR(255) 断线处理人,
    welder VARCHAR(255) 焊线人,
    wait_time_diff NUMERIC(64,4) 断线等待时间（min),
    handle_time_diff NUMERIC(64,4) 断线已处理时间(min),
    exception_time_diff NUMERIC(64,4) 异常时间(min),
    resume_time TIMESTAMP(6) 断线恢复时间,
    break_type VARCHAR(255) 断线类型,
    break_cut_direct VARCHAR(255) 断线切割方向,
    break_handle_mehtod VARCHAR(255) 断线处理方式,
    break_handle_use_line VARCHAR(255) 断线处理用线,
    break_dura VARCHAR(255) 断线处理时间,、
    )

    【表说明】
    实验车间断线记录表：记录生产过程中发生的断线事件详细信息，包括断线原因、处理过程、责任人及恢复情况等数据，用于生产异常分析与改进。

    【字段说明】
    实验车间断线记录表的workdate，是字符串（格式：YYYY-MM-DD），和时间戳比较时，时间戳需要使用TO_CHAR做时间格式转换

    查询实验车间断线记录表的`断线`时，查询条件：break_reason != '断线误报' and break_reason != '自检跳线'

    查询实验车间断线记录表的`断线`时，需要返回字段包括：
    - 刀号
    - 设备编号
    - 断线时间
    - 断线深度
    - 钢线编号
    - 断线描述
    - 断线原因
    - 断线类别
    - 排查情况
    - 工作日
    - 断线切割方向
    - 断线处理方式
    - 断线处理用线
    - 断线处理时间
    - 责任人
    - 产线

    实验车间断线记录表的break_reason，取值包括但不限于以下：
    - 收线边线断线
    - 其它原因断线
    - 跳线断线
    - 无异常放线断线
    - 点检类放线断线
    - 隐裂断线
    - 无异常收线断线
    - 大线弓断线
    - 无异常断线
    - 断线误报
    - 设备异常断线
    - 点检/磨损类断线
    - 点检类收线断线
    - 放线边线断线

    【默认排序方式】
    workdate DESC, breaking_time DESC, device_no ASC

    --------------------------- 【表结构】
    实验车间切片机生产配置表(pds_slicer)(
    id SERIAL 主键,
    machine_code VARCHAR(64) 设备编码,
    prod_line_code VARCHAR(64) 产线编码
    )

    【表说明】
    实验车间切片机生产配置表：记录切片机设备与产线的对应配置关系，用于生产管理与设备调度。

    【字段说明】
    实验车间切片机生产配置表的prod_line_code，取值包括但不限于以下：
    - A
    - B
    - C

    【默认排序方式】
    prod_line_code ASC, machine_code  ASC
    '''},
                    {"role": "user", "content": question},
                    ]
                )
                
                # 提取回复内容
                message_content = response.choices[0].message.content
                
                # 提取SQL语句
                sql_pattern = r"```sql\s*(.*?)\s*```"
                sql_match = re.search(sql_pattern, message_content, re.DOTALL)
                
                if sql_match:
                    sql = sql_match.group(1).strip()
                else:
                    # 如果没有特定格式，尝试提取整个内容
                    sql = message_content.strip()
                
                # 执行SQL查询
                # 此处仅提取SQL，不实际执行
                df = None
                
            except Exception as e:
                error = str(e)
                logger.error(f"生成SQL时出错: {e}")
                
            # 将结果添加到结果列表
            results.append({
                "question_id": question_id,
                "question": question,
                "sql": clean_sql(sql) if sql else None,
                "result": None,
                "error": error
            })
            
            logger.info(f"处理完成：问题ID {question_id}")
        
        # 将结果写入输出JSON文件
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理完成：共处理 {len(results)} 个问题，结果已保存到 {output_file}")
    
    except Exception as e:
        logger.error(f"处理JSON文件时出错：{e}")
        raise


# 定义处理单个任务的函数
def process_task(task):
    try:
        gold_sql = task["gold_sql"]
        predicted_sql = task["predicted_sql"]
        
        # 执行SQL比较
        result = execute_sql_by_postgresql(clean_sql(gold_sql), clean_sql(predicted_sql))
        
        # 返回比较结果
        return {
            "question_id": task["question_id"],
            "question": task["question"],
            "gold_sql": gold_sql,
            "predicted_sql": predicted_sql,
            "result": result["result"] if result else 0,
            "columns_match": result["columns_match"] if result else 0,
            "rows_match": result["rows_match"] if result else 0,
            "gold_sql_error": result["gold_sql_error"] if result else None,
            "predicted_sql_error": result["predicted_sql_error"] if result else None
        }
    except Exception as e:
        logger.error(f"处理任务时出错 ID {task['question_id']}: {e}")
        return {
            "question_id": task["question_id"],
            "question": task["question"],
            "gold_sql": task["gold_sql"],
            "predicted_sql": task["predicted_sql"],
            "result": 0,
            "error": str(e)
        }


def init_process():
    """
    初始化进程，每个进程启动时调用
    """
    # 记录进程启动信息
    process_id = os.getpid()
    logger.info(f"进程 {process_id} 已启动")
    
    # 不再需要初始化数据库连接，因为每次执行SQL时都创建新连接


def compare_sql_parallel(testset_file: str, vanna_results_file: str, output_file: str, cpu_count: int = None) -> None:
    """
    并行比较标准SQL和预测SQL的执行结果
    
    Args:
        testset_file: 标准测试集JSON文件路径
        vanna_results_file: Vanna模型生成结果JSON文件路径
        output_file: 比较结果输出JSON文件路径
        cpu_count: 并行处理的CPU数量，默认为None(使用所有可用CPU)
    """
    try:
        # 如果未指定CPU数量，使用可用的CPU数量减1(预留一个核心)
        if cpu_count is None:
            cpu_count = max(1, multiprocessing.cpu_count() - 1)
        # 限制CPU数量，避免创建过多连接
        cpu_count = min(cpu_count, 8)  # 最多使用8个核心
        logger.info(f"使用 {cpu_count} 个CPU核心进行并行处理")
        
        # 读取测试集和预测结果
        with open(testset_file, 'r', encoding='utf-8') as f:
            testset = json.load(f)
        
        with open(vanna_results_file, 'r', encoding='utf-8') as f:
            vanna_results = json.load(f)
        
        # 创建预测结果的映射表，使用question_id作为键
        vanna_dict = {item.get("question_id"): item for item in vanna_results}
        
        # 创建待处理的任务列表
        tasks = []
        for test_item in testset:
            question_id = test_item.get("question_id")
            question = test_item.get("question")
            gold_sql = test_item.get("SQL")
            
            # 获取对应的预测结果
            if question_id in vanna_dict:
                vanna_item = vanna_dict[question_id]
                predicted_sql = vanna_item.get("sql")
                
                if gold_sql and predicted_sql:
                    # 添加到任务列表
                    tasks.append({
                        "question_id": question_id,
                        "question": question,
                        "gold_sql": clean_sql(gold_sql),
                        "predicted_sql": clean_sql(predicted_sql)
                    })
                else:
                    logger.warning(f"跳过ID {question_id}：标准SQL或预测SQL为空")
            else:
                logger.warning(f"跳过ID {question_id}：未找到对应的预测结果")
                
        # 使用进程池并行执行任务
        total_tasks = len(tasks)
        results = []
        
        # 使用multiprocessing.Pool进行并行处理，并设置进程初始化函数
        with multiprocessing.Pool(processes=cpu_count, initializer=init_process) as pool:
            # 创建一个用于显示进度的回调函数
            def log_result(result):
                results.append(result)
                logger.info(f"进度: {len(results)}/{total_tasks}, ID: {result['question_id']}, 结果: {result['result']}")
            
            # 提交所有任务处理
            for task in tasks:
                pool.apply_async(process_task, (task,), callback=log_result)
            
            # 关闭进程池并等待所有任务完成
            pool.close()
            pool.join()
        
        # 计算统计信息
        total = len(results)
        successful = sum(1 for r in results if r.get("result") == 1)
        success_rate = successful / total if total > 0 else 0
        
        # 打印详细的评分指标
        logger.info("=" * 50)
        logger.info("SQL执行结果比较评分")
        logger.info("=" * 50)
        logger.info(f"总样本数: {total}")
        logger.info(f"成功匹配数: {successful}")
        logger.info(f"失败匹配数: {total - successful}")
        logger.info(f"准确率: {success_rate:.4f} ({successful}/{total})")
        logger.info("=" * 50)
        
        # 将评分指标添加到结果中
        evaluation_summary = {
            "total_samples": total,
            "successful_matches": successful,
            "failed_matches": total - successful,
            "accuracy": success_rate
        }
        
        # 将结果写入输出JSON文件
        output_data = {
            "evaluation_summary": evaluation_summary,
            "results": results
        }
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"SQL比较结果: 总数={total}, 成功={successful}, 成功率={success_rate:.2%}")
        logger.info(f"比较结果已保存到: {output_file}")
        
        return results, evaluation_summary
        
    except Exception as e:
        logger.error(f"比较SQL执行结果时出错: {e}")
        raise


if __name__ == "__main__":
    args_parser = argparse.ArgumentParser()
    args_parser.add_argument("--model", type=str, default="vanna")
    args = args_parser.parse_args()
    
    # 生成测试集
    testset_file = "core/evaluation/testset.json"
    generate_test_set(testset_file)
    
    if args.model == "vanna":
        # 生成NL2SQL结果
        input_file = "core/evaluation/testset.json"
        output_file = "core/evaluation/vanna_results.json"
        process_nl2sql(input_file, output_file)
        
        # SQL比较
        testset_file = "core/evaluation/testset.json"
        vanna_results_file = "core/evaluation/vanna_results.json"
        compare_output_file = "core/evaluation/sql_comparison_results.json"
        compare_sql_parallel(testset_file, vanna_results_file, compare_output_file, cpu_count=4)

    elif args.model == "zeroshot":
        # 生成NL2SQL结果
        input_file = "core/evaluation/testset.json"
        output_file = "core/evaluation/zeroshot_results.json"
        process_nl2sql_by_zero_shot(input_file, output_file)
        
        # SQL比较
        testset_file = "core/evaluation/testset.json"
        vanna_results_file = "core/evaluation/zeroshot_results.json"
        compare_output_file = "core/evaluation/zeroshot_comparison_results.json"
        compare_sql_parallel(testset_file, vanna_results_file, compare_output_file, cpu_count=4)
        
