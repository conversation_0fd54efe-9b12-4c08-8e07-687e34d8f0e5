[{"question_id": 0, "db_id": "pds_attend_record", "question": "查询2025年3月25日宜宾制造部出勤情况", "evidence": "", "SQL": "SELECT DISTINCT user_code as \"工号\", user_name as \"姓名\", user_depart as \"部门\", workdate as \"工作日\", shift_time as \"班次\", create_time as \"创建时间\" FROM pds_attend_record WHERE workdate = '2025-03-25' AND user_depart = '宜宾制造部' ORDER BY create_time desc", "difficulty": "challenging"}, {"question_id": 1, "db_id": "mes_model_break", "question": "查询2025年3月1日至2025年3月4日期间产线C断线情况", "evidence": "", "SQL": "SELECT cut_no as 刀号, device_no as 机台号, breaking_time as 断线时间, \"depth\" as 断线深度, varline as 钢线编码, \"desc\" as 断线描述, break_reason as 断线原因, break_class as 断线类别, cir as 排查情况, workdate as 工作日, break_cut_direct as 断线切割方向, break_handle_mehtod as 断线处理方式, break_handle_use_line as 断线处理用线, break_dura as 断线处理时间, person as 责任人, s.prod_line_code AS 产线 FROM mes_model_break b LEFT JOIN pds_slicer s ON b.device_no = s.machine_code WHERE workdate >= '2025-03-01' AND workdate <= '2025-03-04' AND s.prod_line_code = 'C'", "difficulty": "challenging"}, {"question_id": 2, "db_id": "mes_model_break", "question": "查询今天断线情况", "evidence": "", "SQL": "SELECT cut_no AS 刀号, device_no AS 设备编号, breaking_time AS 断线时间, DEPTH AS 断线深度, varline AS 钢线编号, \"desc\" AS 断线描述, break_reason AS 断线原因, break_class AS 断线类别, cir AS 排查情况, workdate AS 工作日, break_cut_direct AS 断线切割方向, break_handle_mehtod AS 断线处理方式, break_handle_use_line AS 断线处理用线, break_dura AS 断线处理时间, person AS 责任人, s.prod_line_code AS 产线 FROM mes_model_break b LEFT JOIN pds_slicer s ON b.device_no = s.machine_code WHERE TO_DATE( workdate, 'YYYY-MM-DD' ) = CURRENT_DATE", "difficulty": "challenging"}, {"question_id": 3, "db_id": "mes_model_break", "question": "查询今天断线为无异常断线的数据", "evidence": "", "SQL": "SELECT cut_no as 刀号, device_no as 设备编号, breaking_time as 断线时间, depth as 断线深度, varline as 钢线编号, \"desc\" as 断线描述, break_reason as 断线原因, break_class as 断线类别, cir as 排查情况, workdate as 工作日, break_cut_direct as 断线切割方向, break_handle_mehtod as 断线处理方式, break_handle_use_line as 断线处理用线, break_dura as 断线处理时间, person as 责任人, s.prod_line_code AS 产线 FROM mes_model_break b LEFT JOIN pds_slicer s ON b.device_no = s.machine_code WHERE TO_DATE(workdate, 'YYYY-MM-DD') = CURRENT_DATE AND break_reason LIKE '%无异常断线%'", "difficulty": "challenging"}, {"question_id": 4, "db_id": "pds_all_round_report", "question": "查询机台FQ21今天的良率情况（按刀号区分）", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, ROUND(SUM(a_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(b_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND(SUM(c_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(d_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(a_amount::numeric) - SUM(a0_amount::numeric)) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(a_amount::numeric) + SUM(b_amount::numeric) + SUM(c_amount::numeric) + SUM(d_amount::numeric)) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS 直通率 FROM pds_all_round_report WHERE fx_workdate = CURRENT_DATE AND slice_mch_no = 'FQ21' GROUP BY knife_sn ORDER BY knife_sn;", "difficulty": "challenging"}, {"question_id": 5, "db_id": "pds_all_round_report", "question": "查询今天产线A的良率情况", "evidence": "", "SQL": "SELECT ar.knife_sn AS 刀号, ps.prod_line_code AS 产线, ROUND(SUM(ar.a_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 直通率 FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate = CURRENT_DATE AND ps.prod_line_code = 'A' GROUP BY ps.prod_line_code, ar.knife_sn", "difficulty": "challenging"}, {"question_id": 6, "db_id": "pds_all_round_report", "question": "查询今天各产线的良率情况", "evidence": "", "SQL": "SELECT ps.prod_line_code AS 产线, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate = CURRENT_DATE GROUP BY ps.prod_line_code", "difficulty": "challenging"}, {"question_id": 7, "db_id": "pds_all_round_report", "question": "查询2025-3-10至2025-3-12，切片机编号为FQ02的良率情况", "evidence": "", "SQL": "SELECT ar.fx_workdate AS 工作日期, ps.prod_line_code AS 产线, ar.knife_sn AS 刀号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate BETWEEN '2025-03-10' AND '2025-03-12' AND ar.slice_mch_no = 'FQ02' GROUP BY ar.fx_workdate, ps.prod_line_code, ar.knife_sn ORDER BY ar.fx_workdate, ar.knife_sn;", "difficulty": "challenging"}, {"question_id": 8, "db_id": "pds_all_round_report", "question": "FQ01最新3刀的刀号及良率", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, ROUND(SUM(a_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(b_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND(SUM(c_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(d_amount::numeric) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(a_amount::numeric) - SUM(a0_amount::numeric)) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(a_amount::numeric) + SUM(b_amount::numeric) + SUM(c_amount::numeric) + SUM(d_amount::numeric)) / SUM(theoretical_amount::NUMERIC) * 100, 2) AS 直通率 FROM pds_all_round_report WHERE slice_mch_no = 'FQ01' AND fx_datetime IS NOT NULL GROUP BY knife_sn ORDER BY MAX(fx_datetime) DESC LIMIT 3;", "difficulty": "challenging"}, {"question_id": 9, "db_id": "pds_all_round_report", "question": "小车间2025年4月26XG和26FD的良率汇总情况", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate >= '2025-04-01' AND ar.fx_workdate < '2025-05-01' AND (ar.varline_sn LIKE '%26XG%' OR ar.varline_sn LIKE '%26FD%') GROUP BY substring(ar.varline_sn,3,4)", "difficulty": "challenging"}, {"question_id": 10, "db_id": "pds_all_round_report", "question": "查询 2025年3月 小车间 26XG 与 26FD的良率汇总", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate >= '2025-03-01' AND ar.fx_workdate < '2025-04-01' AND (ar.varline_sn LIKE '%26XG%' OR ar.varline_sn LIKE '%26FD%') GROUP BY substring(ar.varline_sn,3,4)", "difficulty": "challenging"}, {"question_id": 11, "db_id": "pds_all_round_report", "question": "查询 2025年4月26日 当天 26XG 与 26FD的良率数据", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate = '2025-04-26' AND (ar.varline_sn LIKE '%26XG%' OR ar.varline_sn LIKE '%26FD%') GROUP BY substring(ar.varline_sn,3,4)", "difficulty": "challenging"}, {"question_id": 12, "db_id": "pds_all_round_report", "question": "查询 近期（近7天）26XG、26FD钢线的良率", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate >= CURRENT_DATE - INTERVAL '7 days' AND ar.fx_workdate < CURRENT_DATE AND (ar.varline_sn LIKE '%26XG%' OR ar.varline_sn LIKE '%26FD%') GROUP BY substring(ar.varline_sn,3,4)", "difficulty": "challenging"}, {"question_id": 13, "db_id": "pds_all_round_report", "question": "查询 2025年4月26日 钢线类型为 “XG” 的所有钢线良率", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate = '2025-04-26' AND ar.varline_sn LIKE '%XG%' GROUP BY substring(ar.varline_sn,3,4)", "difficulty": "challenging"}, {"question_id": 14, "db_id": "pds_all_round_report", "question": "查询今天FQ28号机良率和小指标情况", "evidence": "", "SQL": "SELECT ar.knife_sn AS 刀号, ROUND(SUM(ar.a_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND((SUM(ar.b_amount::NUMERIC)+SUM(ar.a_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS AB率, ROUND(SUM(ar.c_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_workdate = CURRENT_DATE AND ar.slice_mch_no = 'FQ28' GROUP BY ar.knife_sn ORDER BY ar.knife_sn", "difficulty": "challenging"}, {"question_id": 15, "db_id": "pds_all_round_report", "question": "查询昨天实验车间加切率与卡线率的情况", "evidence": "", "SQL": "SELECT ROUND(COUNT(CASE WHEN TRIM(exception1) <> '' THEN 1 END) * 100.0 / COUNT(*), 2) AS \"加切异常占比_百分比\", ROUND(COUNT(CASE WHEN TRIM(exception2) <> '' THEN 1 END) * 100.0 / COUNT(*), 2) AS \"卡线异常占比_百分比\" FROM pds_all_round_report WHERE slice_begintime >= date_trunc('day', now()) - interval '1 day' + interval '8 hour' AND slice_begintime < date_trunc('day', now()) + interval '8 hour';", "difficulty": "challenging"}, {"question_id": 16, "db_id": "pds_all_round_report", "question": "查询今天产线A和B良率和小指标情况", "evidence": "", "SQL": "SELECT ps.prod_line_code AS 产线, ROUND(SUM(ar.a_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND((SUM(ar.b_amount::NUMERIC) + SUM(ar.a_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS AB率, ROUND(SUM(ar.c_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate = CURRENT_DATE AND ps.prod_line_code in ('A', 'B') GROUP BY ps.prod_line_code", "difficulty": "challenging"}, {"question_id": 17, "db_id": "pds_all_round_report", "question": "查询2025年4月15日小车间BC线良率和小指标", "evidence": "", "SQL": "SELECT ps.prod_line_code AS 产线, ROUND(SUM(ar.a_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS B率, ROUND((SUM(ar.b_amount::NUMERIC) + SUM(ar.a_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS AB率, ROUND(SUM(ar.c_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / SUM(ar.theoretical_amount::NUMERIC) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate = '2025-4-15' AND ps.prod_line_code in ('B', 'C') GROUP BY ps.prod_line_code", "difficulty": "challenging"}, {"question_id": 18, "db_id": "pds_all_round_report", "question": "查询实验车间2025-4-15 C线26FD线各机台正A率", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ps.prod_line_code AS 产线, substring(ar.varline_sn, 3, 4) AS 钢线编号, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率 FROM pds_all_round_report ar JOIN pds_slicer ps ON ar.slice_mch_no = ps.machine_code WHERE ar.fx_workdate = '2025-4-15' AND ar.varline_sn LIKE '%26FD%' AND ps.prod_line_code = 'C' GROUP BY ar.slice_mch_no, ps.prod_line_code, substring(ar.varline_sn, 3, 4) ORDER BY ar.slice_mch_no, substring(ar.varline_sn, 3, 4);", "difficulty": "challenging"}, {"question_id": 19, "db_id": "pds_all_round_report", "question": "小车间2024年12月1号到2025年3月6号的量产断线率汇总", "evidence": "", "SQL": "SELECT SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END) AS 量产总刀数, COUNT(DISTINCT mb.cut_no) AS 断线总刀数, round(1.0 * COUNT(DISTINCT mb.cut_no) / SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END)*100,3) AS 断线率 FROM pds_all_round_report ar LEFT JOIN mes_model_break mb ON ar.knife_sn = mb.cut_no and mb.break_reason != '断线误报' and mb.break_reason != '自检跳线' and ar.slice_type = '量产' WHERE ar.fx_workdate BETWEEN '2024-12-01' AND '2025-03-06';", "difficulty": "challenging"}, {"question_id": 20, "db_id": "pds_all_round_report", "question": "小车间2024年12月2号到2025年3月7号的量产机台断线率情况", "evidence": "", "SQL": "SELECT SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END) AS 量产总刀数, COUNT(DISTINCT mb.cut_no) AS 断线总刀数, round(1.0 * COUNT(DISTINCT mb.cut_no) / SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END)*100,3) AS 断线率 FROM pds_all_round_report ar LEFT JOIN mes_model_break mb ON ar.knife_sn = mb.cut_no and mb.break_reason != '断线误报' and mb.break_reason != '自检跳线' and ar.slice_type = '量产' WHERE ar.fx_workdate BETWEEN '2024-12-02' AND '2025-03-07';", "difficulty": "challenging"}, {"question_id": 21, "db_id": "pds_all_round_report", "question": "小车间2024年12月1号到2025年3月6号的研发机台断线率汇总", "evidence": "", "SQL": "SELECT SUM(CASE WHEN ar.slice_type = '研发测试' THEN 1 ELSE 0 END) AS 研发总刀数, COUNT(DISTINCT mb.cut_no) AS 断线总刀数, round(1.0 * COUNT(DISTINCT mb.cut_no) / SUM(CASE WHEN ar.slice_type = '研发测试' THEN 1 ELSE 0 END)*100,3) AS 断线率 FROM pds_all_round_report ar LEFT JOIN mes_model_break mb ON ar.knife_sn = mb.cut_no and mb.break_reason != '断线误报' and mb.break_reason != '自检跳线' and ar.slice_type = '研发测试' WHERE ar.fx_workdate BETWEEN '2024-12-01' AND '2025-03-06';", "difficulty": "challenging"}, {"question_id": 22, "db_id": "pds_all_round_report", "question": "今天小车间钢线编号为24线的良率汇总", "evidence": "", "SQL": "SELECT substring(ar.varline_sn, 3, 4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate = CURRENT_DATE AND ar.varline_sn LIKE '%24%' GROUP BY substring(ar.varline_sn, 3, 4) HAVING substring(ar.varline_sn, 3, 4) LIKE '%24%'", "difficulty": "challenging"}, {"question_id": 23, "db_id": "pds_all_round_report", "question": "查询小车间最近10天FQ01机台新主辊上机前15刀正A率", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, wheel_slot_sort AS 主辊切割次数, slice_begintime AS 切割时间, ROUND((SUM(a_amount::NUMERIC) - SUM(a0_amount::NUMERIC)) / NULLIF(SUM(theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率 FROM pds_all_round_report WHERE slice_endtime > CURRENT_DATE - INTERVAL '10 days' AND slice_endtime <= CURRENT_TIMESTAMP AND slice_mch_no = 'FQ01' AND wheel_slot_sort <= 15 GROUP BY knife_sn, wheel_slot_sort, slice_begintime", "difficulty": "challenging"}, {"question_id": 24, "db_id": "pds_all_round_report", "question": "查询最近10天FQ19机台新主辊上机 前30刀断线率", "evidence": "", "SQL": "SELECT 'FQ19' AS 机台号, MAX(ar.wheel_slot_sort) AS 主辊最新切割次数, COUNT(DISTINCT mb.cut_no) AS 断线刀数, COUNT(DISTINCT ar.knife_sn) AS 切割刀数, ROUND(COUNT(DISTINCT mb.cut_no)::NUMERIC / NULLIF(COUNT(DISTINCT ar.knife_sn), 0) * 100, 2) AS 断线率 FROM pds_all_round_report ar LEFT JOIN mes_model_break mb ON ar.knife_sn = mb.cut_no AND mb.break_reason NOT IN ('断线误报', '自检跳线') WHERE ar.fx_workdate > CURRENT_DATE - INTERVAL '10 days' AND ar.fx_workdate <= CURRENT_DATE AND ar.slice_mch_no = 'FQ19' AND ar.wheel_slot_sort <= 30;", "difficulty": "challenging"}, {"question_id": 25, "db_id": "pds_all_round_report", "question": "查询实验车间FQ02机台新主辊上机后，前30刀正A率", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, wheel_slot_sort AS 主辊切割次数, slice_begintime AS 切割时间, ROUND((SUM(a_amount::NUMERIC) - SUM(a0_amount::NUMERIC)) / NULLIF(SUM(theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率 FROM pds_all_round_report rr WHERE slice_mch_no = 'FQ02' AND wheel_slot_sort <= 30 AND wheel_slot_code = (SELECT wheel_slot_code FROM pds_all_round_report r1 WHERE r1.slice_mch_no=rr.slice_mch_no AND wheel_slot_code IS NOT NULL ORDER BY create_time DESC LIMIT 1 ) GROUP BY knife_sn, wheel_slot_sort, slice_begintime", "difficulty": "challenging"}, {"question_id": 26, "db_id": "pds_all_round_report", "question": "FQ10机台新主辊上机前30刀正A率", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, wheel_slot_sort AS 主辊切割次数, slice_begintime AS 切割时间, ROUND((SUM(a_amount::NUMERIC) - SUM(a0_amount::NUMERIC)) / NULLIF(SUM(theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率 FROM pds_all_round_report rr WHERE slice_mch_no = 'FQ10' AND wheel_slot_sort <= 30 AND wheel_slot_code = (SELECT wheel_slot_code FROM pds_all_round_report r1 WHERE r1.slice_mch_no=rr.slice_mch_no AND wheel_slot_code IS NOT NULL ORDER BY create_time DESC LIMIT 1 ) GROUP BY knife_sn, wheel_slot_sort, slice_begintime", "difficulty": "challenging"}, {"question_id": 27, "db_id": "pds_all_round_report", "question": "查询FQ24更换主辊后前15刀的正A率", "evidence": "", "SQL": "SELECT knife_sn AS 刀号, wheel_slot_sort AS 主辊切割次数, slice_begintime AS 切割时间, ROUND((SUM(a_amount::NUMERIC) - SUM(a0_amount::NUMERIC)) / NULLIF(SUM(theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率 FROM pds_all_round_report rr WHERE slice_mch_no = 'FQ24' AND wheel_slot_sort <= 15 AND wheel_slot_code = (SELECT wheel_slot_code FROM pds_all_round_report r1 WHERE r1.slice_mch_no=rr.slice_mch_no AND wheel_slot_code IS NOT NULL ORDER BY create_time DESC LIMIT 1 ) GROUP BY knife_sn, wheel_slot_sort, slice_begintime", "difficulty": "challenging"}, {"question_id": 28, "db_id": "pds_all_round_report", "question": "4月17日量产24XG切割正A率以及各项小指标占比", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_workdate = '2025-04-17' AND ar.slice_type = '量产' AND ar.varline_sn LIKE '%24XG%' GROUP BY ar.slice_mch_no", "difficulty": "challenging"}, {"question_id": 29, "db_id": "pds_all_round_report", "question": "4月16日量产24FD切割正A率以及各项小指标占比", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_workdate = '2025-04-16' AND ar.slice_type = '量产' AND ar.varline_sn LIKE '%24FD%' GROUP BY ar.slice_mch_no", "difficulty": "challenging"}, {"question_id": 30, "db_id": "pds_all_round_report", "question": "4月16日研发测试24FD切割正A率以及各项小指标占比", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_workdate = '2025-04-16' AND ar.slice_type = '研发测试' AND ar.varline_sn LIKE '%24XG%' GROUP BY ar.slice_mch_no", "difficulty": "challenging"}, {"question_id": 31, "db_id": "pds_all_round_report", "question": "2025年4月15日夜班24XG量产良率", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_datetime >= '2025-04-15 20:00:00' and ar.fx_datetime < '2025-04-16 08:00:00' AND ar.slice_type = '量产' AND ar.varline_sn LIKE '%24XG%' GROUP BY ar.slice_mch_no", "difficulty": "challenging"}, {"question_id": 32, "db_id": "pds_all_round_report", "question": "2025年4月15日白班26FD量产良率", "evidence": "", "SQL": "SELECT ar.slice_mch_no AS 切片机编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率, ROUND((SUM(ar.a0_amount_linemarks::NUMERIC) + SUM(ar.a1_amount_linemarks::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-线痕\", ROUND(SUM(ar.c_hd::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"C厚度\", ROUND(SUM(ar.a0_sc::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"A-色差\", ROUND((SUM(ar.dirty_face_amount::NUMERIC) + SUM(ar.dirty_heavy_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"脏污率\", ROUND(SUM(ar.collapse_total::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"崩边率\", ROUND(SUM(ar.b_zl::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"直流率\", ROUND(SUM(ar.d_missangle::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"缺角率\", ROUND(SUM(ar.yl_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"隐裂率\", ROUND(SUM(ar.b_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"B其他率\", ROUND(SUM(ar.d_other::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS \"D其他率\" FROM pds_all_round_report ar WHERE ar.fx_datetime >= '2025-04-15 08:00:00' and ar.fx_datetime < '2025-04-16 20:00:00' AND ar.slice_type = '量产' AND ar.varline_sn LIKE '%26FD%' GROUP BY ar.slice_mch_no", "difficulty": "challenging"}, {"question_id": 33, "db_id": "pds_all_round_report", "question": "2025-4-20 机台FQ21卡线刀次", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate = '2025-4-20' AND slice_mch_no = 'FQ21'", "difficulty": "challenging"}, {"question_id": 34, "db_id": "pds_all_round_report", "question": "查询 2025-4-19 机台 FQ21 的卡线刀次", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate = '2025-4-19' AND slice_mch_no = 'FQ21';", "difficulty": "challenging"}, {"question_id": 35, "db_id": "pds_all_round_report", "question": "2025-4-20 机台 FQ22 的卡线刀次", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate = '2025-4-20' AND slice_mch_no = 'FQ22';", "difficulty": "challenging"}, {"question_id": 36, "db_id": "pds_all_round_report", "question": "统计 FQ21 机台最近一周每天的卡线异常数量", "evidence": "", "SQL": "SELECT fx_workdate AS \"工作日\", COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE slice_mch_no = 'FQ21' AND fx_workdate BETWEEN CURRENT_DATE - INTERVAL '6 day' AND CURRENT_DATE GROUP BY fx_workdate ORDER BY fx_workdate;", "difficulty": "challenging"}, {"question_id": 37, "db_id": "pds_all_round_report", "question": "2025年4月小车间24线型和26线型良率对比", "evidence": "", "SQL": "SELECT substring(ar.varline_sn, 3, 4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate >= '2025-04-01' AND ar.fx_workdate < '2025-05-01' AND (ar.varline_sn LIKE '%24%' OR ar.varline_sn LIKE '%26%') GROUP BY substring(ar.varline_sn, 3, 4) HAVING substring(ar.varline_sn, 3, 4) LIKE '%24%' OR substring(ar.varline_sn, 3, 4) LIKE '%26%'", "difficulty": "challenging"}, {"question_id": 38, "db_id": "pds_all_round_report", "question": "今天小车间钢线编号为26线的良率汇总", "evidence": "", "SQL": "SELECT substring(ar.varline_sn,3,4) AS 钢线编号, ROUND(SUM(ar.a_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS A率, ROUND(SUM(ar.b_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS B率, ROUND(SUM(ar.c_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS C率, ROUND(SUM(ar.d_amount::NUMERIC) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS D率, ROUND((SUM(ar.a_amount::NUMERIC) - SUM(ar.a0_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 正A率, ROUND((SUM(ar.a_amount::NUMERIC) + SUM(ar.b_amount::NUMERIC) + SUM(ar.c_amount::NUMERIC) + SUM(ar.d_amount::NUMERIC)) / NULLIF(SUM(ar.theoretical_amount::NUMERIC), 0) * 100, 2) AS 直通率 FROM pds_all_round_report ar WHERE ar.fx_workdate = CURRENT_DATE AND ar.varline_sn LIKE '%26%' GROUP BY substring(ar.varline_sn,3,4) HAVING substring(ar.varline_sn,3,4) LIKE '%26%'", "difficulty": "challenging"}, {"question_id": 39, "db_id": "pds_all_round_report", "question": "小车间3月份卡线情况", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate >= '2025-03-01' AND fx_workdate < '2025-04-01';", "difficulty": "challenging"}, {"question_id": 40, "db_id": "pds_all_round_report", "question": "3月份，小车间的卡线异常数量和总切割刀次是多少？", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate >= '2025-03-01' AND fx_workdate < '2025-04-01';", "difficulty": "challenging"}, {"question_id": 41, "db_id": "pds_all_round_report", "question": "昨天，小车间发生的卡线异常次数和总切割刀次分别是多少？", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常次数\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate = CURRENT_DATE - INTERVAL '1 day';", "difficulty": "challenging"}, {"question_id": 42, "db_id": "pds_all_round_report", "question": "查询本月小车间的卡线异常情况", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN exception2 IS NOT NULL AND exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\" FROM pds_all_round_report WHERE fx_workdate >= DATE_TRUNC('month', CURRENT_DATE) AND fx_workdate < DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month');", "difficulty": "challenging"}, {"question_id": 43, "db_id": "pds_all_round_report", "question": "小车间4月份卡线断线情况，区分研发测试与量产机台", "evidence": "", "SQL": "SELECT COUNT(CASE WHEN ar.exception2 IS NOT NULL AND ar.exception2 <> '' THEN 1 END) AS \"卡线异常数量\", COUNT(*) AS \"总切割刀次\", SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END) AS \"量产总刀数\", COUNT(DISTINCT mb.cut_no) AS \"断线总刀数\", ROUND(1.0 * COUNT(DISTINCT mb.cut_no) / NULLIF(SUM(CASE WHEN ar.slice_type = '量产' THEN 1 ELSE 0 END), 0)*100, 3) AS \"量产断线率\", SUM(CASE WHEN ar.slice_type = '研发测试' THEN 1 ELSE 0 END) AS \"研发总刀数\", COUNT(DISTINCT CASE WHEN ar.slice_type = '研发测试' THEN mb.cut_no END) AS \"研发断线总刀数\", ROUND(1.0 * COUNT(DISTINCT CASE WHEN ar.slice_type = '研发测试' THEN mb.cut_no END) / NULLIF(SUM(CASE WHEN ar.slice_type = '研发测试' THEN 1 ELSE 0 END), 0)*100, 3) AS \"研发断线率\" FROM pds_all_round_report ar LEFT JOIN mes_model_break mb ON ar.knife_sn = mb.cut_no AND mb.break_reason != '断线误报' AND mb.break_reason != '自检跳线' WHERE ar.fx_workdate >= '2025-04-01' AND ar.fx_workdate < '2025-05-01';", "difficulty": "challenging"}]