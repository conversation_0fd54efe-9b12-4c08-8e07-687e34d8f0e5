import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session
import os
from core.database.models import <PERSON>t<PERSON><PERSON><PERSON>, UserChatMap, PersonalKnowledge, EsPersonFile


def create_user_chat_map(db: Session, user_id: str, session_id: str):
    """创建用户和会话的映射关系"""
    existing_map = db.query(UserChatMap).filter(
        UserChatMap.session_id == session_id,
    ).first()

    if existing_map:
        return existing_map

    user_chat_map = UserChatMap(
        session_id=session_id,
        user_id=user_id,
        create_time=datetime.now()
    )

    db.add(user_chat_map)
    db.commit()
    db.refresh(user_chat_map)

    return user_chat_map


def get_user_chat_map(db: Session, user_id: str):
    """获取用户的所有会话映射"""
    return db.query(UserChatMap).filter(
        UserChatMap.user_id == user_id,
        UserChatMap.is_deleted == False
    ).order_by(UserChatMap.create_time).all()


def get_user_conversation_id(db: Session, session_id: str):
    return db.query(UserChatMap.conversation_id).filter(
        UserChatMap.session_id == session_id
    ).order_by(UserChatMap.create_time).first()


def update_conversation_id(db: Session, session_id: str, new_conversation_id: str):
    # 查找对应的记录并更新
    db.query(UserChatMap).filter(
        UserChatMap.session_id == session_id
    ).update({
        UserChatMap.conversation_id: new_conversation_id
    })
    db.commit()


def delete_user_chat(db: Session, session_id: str):
    """删除用户和会话的映射关系"""
    db.query(UserChatMap).filter(
        UserChatMap.session_id == session_id,
        UserChatMap.is_deleted == False
    ).update({
        "is_deleted": True,
    })
    db.commit()


def get_chat_history(db: Session, session_id: str):
    """获取特定会话的聊天历史"""
    return db.query(ChatHistory).filter(
        ChatHistory.session_id == session_id,
    ).order_by(ChatHistory.id).all()


def get_personal_knowledge(db: Session, user_id: str):
    """获取用户的个人知识库"""
    return db.query(PersonalKnowledge).filter(
        PersonalKnowledge.user_id == user_id,
    ).all()


def add_personal_knowledge(db: Session, user_id: str, kb_id: uuid):
    """添加用户的个人知识库"""
    existing_kb = db.query(PersonalKnowledge).filter(
        PersonalKnowledge.user_id == user_id,
    ).first()

    if existing_kb:
        return existing_kb

    kb = PersonalKnowledge(
        user_id=user_id,
        kb_id=kb_id,
        kb_name=""
    )

    db.add(kb)
    db.commit()
    db.refresh(kb)

    return kb


def update_file_flag(db: Session, file_id: int, new_flag: int):
    """更新文件的解析状态 flag"""
    print(file_id)
    file_ids = db.query(EsPersonFile.file_id).all()
    # print(f"当前存在的文件ID列表: {file_ids}")

    file_record = db.query(EsPersonFile).filter(
        EsPersonFile.file_id == file_id
    ).first()

    if file_record:
        file_record.flag = new_flag
        db.commit()


def update_file_domain(db: Session, file_id: int, domain: str):
    """更新文件的领域"""
    print(file_id)
    file_ids = db.query(EsPersonFile.file_id).all()
    # print(f"当前存在的文件ID列表: {file_ids}")

    file_record = db.query(EsPersonFile).filter(
        EsPersonFile.file_id == file_id
    ).first()

    if file_record:
        file_record.domain = domain
        file_record.update_time = datetime.now()
        file_record.type = 1
        db.commit()


def mark_file_as_deleted(db: Session, file_id: int):
    """将文件的 deleted 字段更新为 1，表示删除"""
    file_record = db.query(EsPersonFile).filter(
        EsPersonFile.file_id == file_id
    ).first()

    if file_record:
        file_record.deleted = 1
        db.commit()
        db.refresh(file_record)

    return file_record


def get_es_person_job_number(db: Session, file_id: int):
    """获取文件的 job_number"""
    return db.query(EsPersonFile.job_number, EsPersonFile.domain).filter(
        EsPersonFile.file_id == file_id
    ).first()


def update_file_content(db: Session, file_id: int, new_content: str):
    """更新文件的 content 字段"""
    file_record = db.query(EsPersonFile).filter(
        EsPersonFile.file_id == file_id
    ).first()

    if file_record:
        file_record.content = new_content
        db.commit()
        db.refresh(file_record)

    return file_record


def get_all_chats(db: Session):
    return db.query(ChatHistory).order_by(ChatHistory.session_id, ChatHistory.created_at).all()
