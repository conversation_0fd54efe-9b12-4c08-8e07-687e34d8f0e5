import json

from core.config.logging import logger
from core.text2sql.BI.BI_Vanna import BIVannaWithCorrection
from core.text2sql.model import MyVannaWithCorrection

vn_with_correction = MyVannaWithCorrection()
bi_vn_with_correction = BIVannaWithCorrection()


def chat_sql(user_ques, message, **kwargs):
    query = message[0]["content"]
    try_times = 1
    fig_json = ""

    for i in range(try_times):
        try:
            if kwargs.get("bi"):
                sql, df, error, fig_json = bi_vn_with_correction.ask_with_correction(
                    user_ques,
                    auto_train=False,
                    visualize=False,
                    print_results=False,
                    role=kwargs.get("role", []),
                )
            else:
                sql, df, error = vn_with_correction.ask_with_correction(
                    user_ques,
                    auto_train=False,
                    visualize=False,
                    print_results=False,
                    role=kwargs.get("role", []),
                )
            if not sql:
                return error

            logger.info(f"查询语句：{user_ques}, 生成sql：\n {sql} \n")

            markdown_text = df.to_markdown(tablefmt="github", index=False)

            # logger.info(f"markdown_text: \n {markdown_text}")

            if kwargs.get("chat_history"):
                # 保存原始用户查询到历史记录
                kwargs["chat_history"].add_user_message(user_ques)
                kwargs["chat_history"].add_ai_message(json.dumps({"query": markdown_text, "fig": fig_json}, ensure_ascii=False))

            return {
                "table_element": markdown_text,
                "fig": fig_json
            }
            # return markdown_text

        except Exception as e:
            logger.error(f"尝试 {i + 1}/{try_times} 失败，错误: {e}")

            if kwargs.get("chat_history"):
                # 保存原始用户查询到历史记录
                kwargs["chat_history"].add_user_message(user_ques)
                kwargs["chat_history"].add_ai_message("小高AI暂时无法查询到相关数据，点击下方的反馈按钮告诉我们您的需求，我们会尽快为您完善！")

            return {
                "table_element": "小高AI暂时无法查询到相关数据，点击下方的反馈按钮告诉我们您的需求，我们会尽快为您完善！",
                "fig": ""
            }

    return None
