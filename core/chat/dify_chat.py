import json
import re

import httpx
import requests

from core.database.repositories import update_conversation_id
from core.database.session import SessionLocal
from core.config.logging import logger


def chat_dify(user_ques, **kwargs):
    url = 'http://10.26.66.2/v1/chat-messages'
    headers = {
        'Authorization': f'Bearer {kwargs.get("dify_api_key")}',
        'Content-Type': 'application/json'
    }

    data = {
        "inputs": {},
        "query": user_ques,
        "response_mode": "blocking",
        "conversation_id": "" if not kwargs.get("conversation_id") else str(kwargs.get("conversation_id")),
        "user": "未获取到用户" if not kwargs.get("user") else kwargs.get("user"),
        "files": []
    }
    logger.info(f"Dify请求数据结构体：{data}")

    response = requests.post(url, headers=headers, data=json.dumps(data))

    workflow_response = response.json()

    conversation_id = workflow_response.get("conversation_id")
    if conversation_id:
        update_conversation_id(SessionLocal(), session_id=kwargs.get("session_id"), new_conversation_id=conversation_id)

    answer = workflow_response.get("answer")

    # 去除<think>和</think>思考部分
    answer = re.sub(r'<think>.*?</think>', '', answer, flags=re.DOTALL)

    # 提取模型回答中url部分，方便前端渲染
    if kwargs.get("enable_pase_url"):
        urls = re.findall(r'(http[^\s]+)', answer)

        if urls:
            urls_joined = ','.join(urls)
            new_answer = re.sub(r'(http[^\s]+\n?){2}', '', answer)
        else:
            urls_joined = ""
            new_answer = answer
    else:
        urls_joined = ""
        new_answer = answer

    if kwargs.get("chat_history"):
        # 保存原始用户查询到历史记录
        kwargs["chat_history"].add_user_message(user_ques)
        kwargs["chat_history"].add_ai_message(json.dumps({"query": new_answer, "url": urls_joined}, ensure_ascii=False))

    return {"query": new_answer, "url": urls_joined}


async def chat_dify_streaming(user_ques, **kwargs):
    url = 'http://10.26.66.2/v1/chat-messages'
    headers = {
        'Authorization': f'Bearer {kwargs.get("dify_api_key")}',
        'Content-Type': 'application/json'
    }

    data = {
        "inputs": {},
        "query": user_ques,
        "response_mode": "streaming",  # 改为流式模式
        "conversation_id": "" if not kwargs.get("conversation_id") else str(kwargs.get("conversation_id")),
        "user": "未获取到用户" if not kwargs.get("user") else kwargs.get("user"),
        "files": []
    }
    full_answer = ""
    conversation_id = None

    async with httpx.AsyncClient(timeout=None) as client:
        async with client.stream("POST", url, headers=headers, content=json.dumps(data)) as response:
            async for line in response.aiter_lines():
                if line is None:
                    continue

                # 保存对话数据处理逻辑：保留完整对话数据，并更新数据库中的conversation_id
                if line.startswith('data: '):
                    try:
                        json_str = line[6:]
                        chunk_data = json.loads(json_str)

                        if not conversation_id and chunk_data.get("conversation_id"):
                            conversation_id = chunk_data.get("conversation_id")
                            update_conversation_id(SessionLocal(), session_id=kwargs.get("session_id"), new_conversation_id=conversation_id)

                        answer_chunk = chunk_data.get("answer", "")
                        if answer_chunk:
                            full_answer += answer_chunk

                    except json.JSONDecodeError:
                        continue

                yield line + "\n"

    # 整理完整输出
    full_answer = re.sub(r'<think>.*?</think>', '', full_answer, flags=re.DOTALL)

    # URL处理
    if kwargs.get("enable_pase_url"):
        urls = re.findall(r'(http[^\s]+)', full_answer)

        if urls:
            urls_joined = ','.join(urls)
            new_answer = re.sub(r'(http[^\s]+\n?){2}', '', full_answer)
        else:
            urls_joined = ""
            new_answer = full_answer
    else:
        urls_joined = ""
        new_answer = full_answer

    # 保存聊天历史
    if kwargs.get("chat_history"):
        kwargs["chat_history"].add_user_message(user_ques)
        kwargs["chat_history"].add_ai_message(json.dumps({"query": new_answer, "url": urls_joined}, ensure_ascii=False))


async def main():
    async for chunk in chat_dify_streaming(
        "你好，请介绍一下自己",
        dify_api_key="app-7LSAuhCfvIXCNxvZ31pVp1Mf",
        session_id="f07a75a7-a91e-452b-bcab-d09f84fd93d9",
        enable_pase_url=False,
        chat_history=""
    ):
        print(chunk)

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())