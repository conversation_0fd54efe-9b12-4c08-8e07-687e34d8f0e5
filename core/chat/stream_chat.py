import json
import re
import traceback

import openai

import api.endpoints.conversations
from core.config import model_config
from core.config.logging import logger


async def chat_stream(message, think=True, **kwargs):
    """
    处理聊天流式输出

    :param think: 是否启用深度思考
    :param message: 消息历史列表
    :return: 生成器，产生流式响应
    """
    try:
        logger.info(f"concat messages: {message}")

        query = message[-1]["content"]
        message_id = kwargs.get("chat_history")._session_id if kwargs.get("chat_history") else ""
        # 用于收集完整的响应文本
        collected_response = ""

        # 创建流式请求
        if think:
            message[-1]["content"] += " /think"
            responses = api.endpoints.conversations.chat.completions.create(
                model=model_config.think_model_name,
                messages=message,
                stream=True,
                temperature=model_config.think_model_temperature,
                top_p=model_config.think_model_top_p,
                max_tokens=8192,
                presence_penalty=model_config.presence_penalty,
            )
        else:
            message[-1]["content"] += " /no_think"
            responses = api.endpoints.conversations.chat.completions.create(
                model=model_config.no_think_model_name,
                messages=message,
                stream=True,
                temperature=model_config.no_think_model_temperature,
                top_p=model_config.no_think_model_top_p,
                max_tokens=8192,
                presence_penalty=model_config.presence_penalty,
            )

        ###### 处理流式响应 #######
        sse_response = {
            "event": "",
            "message_id": "",
            "answer": ""
        }

        for line in responses:

            if line.choices and hasattr(line.choices[0], 'delta') and line.choices[0].delta.finish_reason == "stop":
                logger.info("检测到流结束标记 finish_reason=stop")
                break

            # 检查客户端是否已断开连接
            if await kwargs.get("request").is_disconnected():
                logger.debug("客户端终止回答，停止发送")
                if kwargs.get("chat_history") and collected_response:
                    # 保存原始用户查询到历史记录
                    kwargs["chat_history"].add_user_message(query)
                    kwargs["chat_history"].add_ai_message(collected_response)
                break

            try:
                # 普通回答
                if line.choices[0].delta.content:
                    answer = line.choices[0].delta.content
                    sse_response["event"] = "message"
                    sse_response["message_id"] = message_id
                    sse_response["answer"] = answer

                    collected_response += answer  # 累加到完整响应

                # 思考内容
                elif line.choices[0].delta.reasoning_content:
                    answer = line.choices[0].delta.reasoning_content
                    sse_response["event"] = "reasoning_content"
                    sse_response["message_id"] = message_id
                    sse_response["answer"] = answer

                    collected_response += answer  # 累加到完整响应

                # 都为空
                else:
                    continue

                yield f"data: {json.dumps(sse_response, ensure_ascii=False)}\n\n".encode('utf-8')

            except AttributeError as e:
                logger.error(f"处理流时发生意外错误: {e}")
                error_data = {"event": "error", "answer": f"服务异常，请稍后再试或联系管理员排查", "message_id": ""}
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
                break

            except Exception as e:
                logger.error(f"处理流时发生意外错误: {e}")
                error_data = {"event": "error", "answer": f"服务异常，请稍后再试或联系管理员排查", "message_id": ""}
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
                break

        if kwargs.get("chat_history") and collected_response:
            # 保存完整响应到历史记录
            kwargs["chat_history"].add_user_message(query)
            kwargs["chat_history"].add_ai_message(collected_response)

        # # 发送完成标记
        # done_data = {
        #     "event": "done",
        #     "message_id": kwargs.get("chat_history")._session_id if kwargs.get("chat_history") else "",
        #     "answer": ""
        # }
        # yield f"data: {json.dumps(done_data, ensure_ascii=False)}\n\n".encode('utf-8')

    except openai.APIConnectionError as e:
        logger.error(f"无法连接到高测模型服务: {e}")
        error_data = {"event": "error", "answer": "网络连接错误，请检查网络或联系管理员排查", "message_id": ""}
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')

    except openai.APIStatusError as e:
        logger.error(f"高测LLM API 返回错误: {e}")
        error_data = {"event": "error", "answer": "网络连接错误，请检查网络或联系管理员排查", "message_id": ""}
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')

    except Exception as e:
        logger.error(f"chat_stream 中发生一般异常: {e}")
        stack_trace = traceback.format_exc()
        logger.error(f"堆栈跟踪信息: {stack_trace}")
        error_data = {"event": "error", "answer": "网络连接错误，请检查网络或联系管理员排查", "message_id": ""}
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')


# async def chat_stream_back(query, message, think=True, **kwargs):
#     """
#     处理聊天流式输出
#
#     :param query:
#     :param think: 是否启用深度思考
#     :param message: 消息历史列表
#     :return: 生成器，产生流式响应
#     """
#     try:
#         logger.info(f"concat messages: {message}")
#
#         # 用于收集完整的响应文本
#         collected_response = ""
#         citation_pattern = re.compile(r"\[citation:\d+\]")  # 匹配引用格式
#         think_start_pattern = re.compile(r"<think>")  # 匹配思考开始标签
#         think_end_pattern = re.compile(r"</think>")  # 匹配思考结束标签
#         formula_start_pattern = re.compile(r"\\\\\(")  # 匹配公式格式
#         formula_end_pattern = re.compile(r"\\\\\)")  # 匹配公式格式
#         buffer = ""
#
#         # 创建流式请求
#         if think:
#             message[-1]["content"] += " /think"
#             responses = model_config.xinference_client.chat.completions.create(
#                 model=model_config.think_model_name,
#                 messages=message,
#                 stream=True,
#                 temperature=model_config.think_model_temperature,
#                 top_p=model_config.think_model_top_p,
#                 max_tokens=8192,
#                 presence_penalty=model_config.presence_penalty,
#             )
#         else:
#             message[-1]["content"] += " /no_think"
#             responses = model_config.xinference_client2.chat.completions.create(
#                 model=model_config.no_think_model_name,
#                 messages=message,
#                 stream=True,
#                 temperature=model_config.no_think_model_temperature,
#                 top_p=model_config.no_think_model_top_p,
#                 max_tokens=8192,
#                 presence_penalty=model_config.presence_penalty,
#             )
#
#         ###### 处理流式响应 #######
#         # 先判断是否需要传递url引用信息
#         if kwargs.get("search_index"):
#             search_index = kwargs.get("search_index")
#
#             # 传递search_index
#             data = {
#                 "type": "search_index",
#                 "msg": search_index
#             }
#             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#         if kwargs.get("search_results"):
#             search_results = kwargs.get("search_results")
#
#             # 传递search_results
#             data = {
#                 "type": "search_results",
#                 "msg": search_results
#             }
#             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#         times = 0
#         for idx, response in enumerate(responses):
#             # print(response)
#             if await kwargs.get("request").is_disconnected():
#                 logger.debug("客户端终止回答，停止发送")
#                 if kwargs.get("chat_history") and collected_response:
#                     # 保存原始用户查询到历史记录
#                     kwargs["chat_history"].add_user_message(query)
#                     kwargs["chat_history"].add_ai_message(collected_response)
#                 break
#
#             try:
#                 # 调试日志，输出完整的响应结构
#                 # logger.debug(f"Response structure: {response}")
#                 # 首先检查是否是流的结束标记
#                 if (response.choices and hasattr(response.choices[0], 'finish_reason')
#                         and response.choices[0].finish_reason == "stop"):
#
#                     logger.info("Detected stream end with finish_reason=stop")
#                     # 存储完整响应到kwargs中，供后续保存到历史记录
#                     if kwargs.get("chat_history") and collected_response:
#                         # 保存原始用户查询到历史记录
#                         kwargs["chat_history"].add_user_message(query)
#                         kwargs["chat_history"].add_ai_message(collected_response)
#
#                     # 检查并发送最后剩余的buffer
#                     if buffer:
#                         data = {"type": "text", "msg": buffer}
#                         collected_response += buffer
#                         yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                     # 发送完成标记
#                     data = {
#                         "type": "Done",
#                         "msg": ""
#                     }
#                     yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#                     break
#
#                 # 然后再检查是否有内容
#                 elif (response.choices
#                       and hasattr(response.choices[0], 'delta')
#                       and (hasattr(response.choices[0].delta, 'content') or hasattr(response.choices[0].delta,
#                                                                                     'reasoning_content'))):
#
#                     content = response.choices[0].delta.content
#
#                     # deepseek-qwen-32-gptq-int8 会有<think>缺失情况
#                     # if times == 0 and hasattr(response.choices[0].delta, 'content') and think:
#                     #     content = "<think>" + content
#
#                     # times += 1
#
#                     if not content:
#                         continue  # 跳过空数据
#
#                     # 检查是否包含可能的标签片段或已有缓存
#                     if "[" in content or "<" in content or r"\\" in content or buffer:
#                         buffer += content  # 累加数据到缓存
#                     else:
#                         data = {"type": "text", "msg": content}
#                         collected_response += content  # 累加到完整响应
#                         yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                     if ("<think>" in buffer
#                             or "</think>" in buffer
#                             or "[citation:" in buffer
#                             or "\\\\(" in buffer
#                             or "\\\\)" in buffer
#                             or len(buffer) >= 15):
#
#                         # 检查缓冲区是否包含[c, 处理[1,2,3]这种情况
#                         left_prentheses_index = buffer.find("[")
#                         if (left_prentheses_index != -1
#                                 and buffer[left_prentheses_index: left_prentheses_index + 2] != "[c"):
#                             data = {"type": "text", "msg": buffer[:left_prentheses_index + 2]}
#                             collected_response += buffer[:left_prentheses_index + 2]  # 累加到完整响应
#                             buffer = buffer[left_prentheses_index + 1:]
#
#                             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#                             continue
#
#                         # 检查缓冲区是否包含<t, 处理<...这种情况
#                         left_prentheses_index = buffer.find("<")
#                         if (left_prentheses_index != -1
#                                 and buffer[left_prentheses_index: left_prentheses_index + 2] != "<t"
#                                 and buffer[left_prentheses_index: left_prentheses_index + 2] != "</"):
#                             data = {"type": "text", "msg": buffer[:left_prentheses_index + 2]}
#                             collected_response += buffer[:left_prentheses_index + 2]  # 累加到完整响应
#                             buffer = buffer[left_prentheses_index + 1:]
#
#                             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#                             continue
#
#                         left_prentheses_index = buffer.find("\\")
#                         if (left_prentheses_index != -1
#                                 and buffer[left_prentheses_index: left_prentheses_index + 3] != "\\\\"):
#                             data = {"type": "text", "msg": buffer[:left_prentheses_index + 2]}
#                             collected_response += buffer[:left_prentheses_index + 2]
#                             buffer = buffer[left_prentheses_index + 1:]
#                             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#                             continue
#
#                         # 优先检查引用标签
#                         citation_match = citation_pattern.search(buffer)
#                         think_start_match = think_start_pattern.search(buffer)
#                         think_end_match = think_end_pattern.search(buffer)
#                         formula_start_match = formula_start_pattern.search(buffer)
#                         formula_end_match = formula_end_pattern.search(buffer)
#
#                         # 处理引用标签
#                         if citation_match:
#                             citation = citation_match.group(0)  # 获取完整引用
#                             before_citation = buffer[:citation_match.start()]  # 引用前的文本
#                             after_citation = buffer[citation_match.end():]  # 引用后的文本
#
#                             if before_citation.strip():
#                                 data = {"type": "text", "msg": before_citation}
#                                 collected_response += before_citation  # 累加到完整响应
#                                 yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             citation_data = {"type": "text", "msg": citation}
#                             collected_response += citation  # 累加到完整响应
#                             yield f"data: {json.dumps(citation_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             buffer = after_citation  # 只保留未发送的部分
#
#                         # 处理思考开始标签
#                         elif think_start_match:
#                             tag = "<think>"
#                             before_tag = buffer[:think_start_match.start()]  # 标签前的文本
#                             after_tag = buffer[think_start_match.end():]  # 标签后的文本
#
#                             if before_tag.strip():
#                                 data = {"type": "text", "msg": before_tag}
#                                 collected_response += before_tag  # 累加到完整响应
#                                 yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             tag_data = {"type": "text", "msg": tag}
#                             collected_response += tag  # 累加到完整响应
#                             yield f"data: {json.dumps(tag_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             buffer = after_tag  # 只保留未发送的部分
#
#                         # 处理思考结束标签
#                         elif think_end_match:
#                             tag = "</think>"
#                             before_tag = buffer[:think_end_match.start()]  # 标签前的文本
#                             after_tag = buffer[think_end_match.end():]  # 标签后的文本
#
#                             if before_tag.strip():
#                                 data = {"type": "text", "msg": before_tag}
#                                 collected_response += before_tag  # 累加到完整响应
#                                 yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             tag_data = {"type": "text", "msg": tag}
#                             collected_response += tag  # 累加到完整响应
#                             yield f"data: {json.dumps(tag_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             buffer = after_tag  # 只保留未发送的部分
#
#                         elif formula_start_match:
#                             tag = "\\\\("
#                             before_tag = buffer[:think_end_match.start()]  # 标签前的文本
#                             after_tag = buffer[think_end_match.end():]  # 标签后的文本
#
#                             if before_tag.strip():
#                                 data = {"type": "text", "msg": before_tag}
#                                 collected_response += before_tag  # 累加到完整响应
#                                 yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             tag_data = {"type": "text", "msg": tag}
#                             collected_response += tag  # 累加到完整响应
#                             yield f"data: {json.dumps(tag_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             buffer = after_tag  # 只保留未发送的部分
#
#                         elif formula_end_match:
#                             tag = "\\\\)"
#                             before_tag = buffer[:think_end_match.start()]  # 标签前的文本
#                             after_tag = buffer[think_end_match.end():]  # 标签后的文本
#
#                             if before_tag.strip():
#                                 data = {"type": "text", "msg": before_tag}
#                                 collected_response += before_tag  # 累加到完整响应
#                                 yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             tag_data = {"type": "text", "msg": tag}
#                             collected_response += tag  # 累加到完整响应
#                             yield f"data: {json.dumps(tag_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#                             buffer = after_tag  # 只保留未发送的部分
#
#                         # 如果缓冲区不再包含标签起始符号，发送剩余内容
#                         if "[" not in buffer and "<" not in buffer and "\\\\)" not in buffer and "\\\\(" not in buffer:
#                             data = {"type": "text", "msg": buffer}
#                             collected_response += buffer  # 累加到完整响应
#                             yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n".encode('utf-8')
#                             buffer = ""
#                 else:
#                     logger.error(f"无法处理响应: {response}")
#
#             except AttributeError as e:
#                 logger.error(f"AttributeError in chat_stream: {e}")
#                 error_data = {
#                     "type": "error",
#                     "msg": f"处理响应时出错: {str(e)}"
#                 }
#                 yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
#                 break
#
#             except Exception as e:
#                 logger.error(f"处理流时发生意外错误: {e}")
#                 error_data = {
#                     "type": "error",
#                     "msg": f"处理响应时出错: {str(e)}"
#                 }
#                 yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
#                 break
#
#     except openai.APIConnectionError as e:
#         logger.error(f"无法连接到 Xinference API: {e}")
#         error_data = {
#             "type": "error",
#             "msg": "无法连接到 Xinference。请检查您的网络连接。"
#         }
#         yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#     except openai.APIStatusError as e:
#         logger.error(f"Xinference API 返回错误: {e}")
#         error_data = {
#             "type": "error",
#             "msg": f"Xinference API 返回错误。状态码: {e.status_code}"
#         }
#         yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
#
#     except Exception as e:
#         logger.error(f"chat_stream 中发生一般异常: {e}")
#         stack_trace = traceback.format_exc()
#         logger.error(f"堆栈跟踪信息: {stack_trace}")
#         error_data = {
#             "type": "error",
#             "msg": f"发生意外错误: {str(e)}"
#         }
#         yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n".encode('utf-8')
