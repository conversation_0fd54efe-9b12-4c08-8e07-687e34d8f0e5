from core.config.logging import logger
from core.config.vector_config import top_k, rerank_top_k
from core.knowledge_base.document_process import DocumentProcessor
from prompts.prompt_manager import DEEPSEEK_FILE_PROMPT


doc_processor = DocumentProcessor()


def prepare_knowledge_enhanced_history(query, history, category, slice_mch=False, knowledge=False, person=False, user_id=None):
    """
    准备知识库检索的历史记录

    Args:


    Returns:
        list: 更新后的对话历史
    """
    similar_docs = []

    if knowledge:
        similar_docs = doc_processor.search_similar_documents(query=query, k=top_k, rerank_k=rerank_top_k, category=category)
    if person:
        person_doc_processor = DocumentProcessor(collection_name=f"{user_id}collection_db")
        similar_docs += person_doc_processor.search_similar_documents(query=query, k=5, rerank_k=10, category=["all"])
    if slice_mch:
        person_doc_processor = DocumentProcessor(collection_name=f"slice_mch_db")
        similar_docs += person_doc_processor.search_similar_documents(query=query, k=5, rerank_k=5, category=["all"])

    logger.info(f"Knowledge search returned {len(similar_docs)} documents")

    file_name = set()
    file_content = ""
    for data in similar_docs:
        file_name.add(data["entity"]["file_name"])
        file_content += data["entity"]["text"] + '\n'

    logger.debug(f"Knowledge search found content from {len(file_name)} unique files")

    query_with_knowledge = DEEPSEEK_FILE_PROMPT.format(
        file_name=file_name if file_name else "",
        file_content=file_content,
        question=query
    )
    history.append({"role": "user", "content": query_with_knowledge})
    return history
