from datetime import datetime
from typing import Union

import requests

from core.config.logging import logger
from prompts.prompt_manager import DEEPSEEK_WEB_PROMPT


def bocha_websearch_tool(query: str, count: int = 10) -> Union[str, list]:
    """
    使用Bocha Web Search API 进行网页搜索。

    参数:
    - query: 搜索关键词
    - freshness: 搜索的时间范围
    - summary: 是否显示文本摘要
    - count: 返回的搜索结果数量

    返回:
    - 搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。
    """

    url = 'https://api.bochaai.com/v1/web-search'
    headers = {
        'Authorization': f'sk-7e98ae233ab8487cb4b335900f2cd253',  # 请替换为你的API密钥
        'Content-Type': 'application/json'
    }
    data = {
        "query": query,
        "freshness": "noLimit",  # 搜索的时间范围，例如 "oneDay", "oneWeek", "oneMonth", "oneYear", "noLimit"
        "summary": True,  # 是否返回长文本摘要
        "count": count
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        json_response = response.json()
        try:
            if json_response["code"] != 200 or not json_response["data"]:
                return f"搜索API请求失败，原因是: {response.msg or '未知错误'}"

            webpages = json_response["data"]["webPages"]["value"]
            if not webpages:
                return "未找到相关结果。"
            formatted_results = []
            for idx, page in enumerate(webpages, start=1):
                formatted_results.append(page)
            return formatted_results
        except Exception as e:
            return f"搜索API请求失败，原因是：搜索结果解析失败 {str(e)}"
    else:
        return f"搜索API请求失败，状态码: {response.status_code}, 错误信息: {response.text}"


async def prepare_history(query, history):
    """
    准备联网搜索的历史记录

    Args:
        query (str): 用户查询
        history (list): 当前的对话历史

    Returns:
        list: 更新后的对话历史
    """
    logger.info(f"Preparing web search for query: '{query[:50]}...'")

    web_docs = bocha_websearch_tool(query)

    if isinstance(web_docs, str):
        logger.warning(f"Web search failed: {web_docs}")
    else:
        logger.info(f"Web search returned {len(web_docs)} results")

    context = ""
    search_index, search_results = [], []
    for idx, info in enumerate(web_docs):
        search_index.append({"url": info["url"], "cite_index": idx})
        search_results.append(info)
        context += f"[webpage {idx} begin]标题: {info['name']}\n内容: {info['summary']}[webpage {idx} end]"

    cur_date = datetime.now().strftime("%Y-%m-%d")
    query_with_web = DEEPSEEK_WEB_PROMPT.format(
        search_results=context,
        cur_date=cur_date,
        question=query
    )
    history.append({"role": "user", "content": query_with_web})
    logger.debug(f"Prepared history with {len(search_results)} search results")
    return history, search_index, search_results


if __name__ == '__main__':
    out = bocha_websearch_tool("今天成都天气如何")
    print(out)