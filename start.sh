#!/bin/bash
source /home/<USER>/envs/algo/anaconda3/etc/profile.d/conda.sh
conda activate es_llm

# 启动 vanna_backstage_server.py
nohup python vanna_backstage_server.py > logs/vanna_backstage_server.log 2>&1 &

nohup python bi_vanna_backstage_server.py > logs/bi_vanna_backstage_server.log 2>&1 &


# 启动 gunicorn
nohup gunicorn app:app -w 16 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:59990 --timeout 1200 > logs/nohup.out 2>&1 &

echo "服务已启动，日志输出到 logs/nohup.out 和 logs/vanna_backstage_server.log 和 logs/bi_vanna_backstage_server.log"
