from fastapi import FastAP<PERSON>, Depends, HTTPException, APIRouter, Request, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, or_  # Added for filtering and aggregation
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTM<PERSON>esponse, JSONResponse
import json
from datetime import datetime, timedelta  # Added for date filtering

from core.database.models import ChatHistory, UserChatMap
from core.database.session import get_db
from core.config.logging import logger

chat_history = APIRouter(prefix="/chat_history", tags=["chat_history"])
templates = Jinja2Templates(directory="templates")


@chat_history.get("/", response_class=HTMLResponse)
async def get_chat_history_page(request: Request):
    """渲染对话历史页面，不包含数据，数据通过API获取"""
    return templates.TemplateResponse("chat_history.html", {"request": request})


@chat_history.get("/api/data", response_class=JSONResponse)
async def get_chat_history_data(
        request: Request,
        db: Session = Depends(get_db),
        page: int = Query(1, ge=1, description="Page number"),
        page_size: int = Query(5, ge=1, le=100, description="Number of items per page"),
        search_text: str = Query(None, description="Search by User ID or content snippet"),
        date_filter: str = Query("all", description="Filter by date: all, today, week, month"),
        message_count_filter: str = Query("all", description="Filter by message count: all, short, medium, long")
):
    """
    获取聊天历史会话摘要数据的API端点 (分页和过滤)
    """
    logger.info(f"接收到获取聊天历史数据请求 - IP: {request.client.host}, Page: {page}, PageSize: {page_size}")

    # Base query for session summaries
    # We want user_id, session_id, last_update, and message_count for each session
    # Using a subquery to get the last_update and message_count per session
    subquery = db.query(
        ChatHistory.session_id,
        func.max(ChatHistory.created_at).label("last_update"),
        func.count(ChatHistory.id).label("message_count")  # Assuming ChatHistory.id is primary key
    ).group_by(ChatHistory.session_id).subquery()

    query = db.query(
        UserChatMap.user_id,
        UserChatMap.session_id,
        subquery.c.last_update,
        subquery.c.message_count
    ).join(
        subquery, UserChatMap.session_id == subquery.c.session_id
    ).filter(UserChatMap.is_deleted == "f")

    # Apply Filters
    if search_text:
        # Note: Searching message content here would require joining ChatHistory again
        # and can be slow. For now, let's assume search_text primarily targets user_id.
        # If you need to search content efficiently, consider full-text search capabilities
        # of your database or a dedicated search engine.
        # For simplicity, we'll just search user_id here.
        # To search message content snippet, it would be more complex and might need
        # to fetch messages, which we are trying to avoid at this summary stage.
        # A compromise could be searching for users whose sessions *contain* the text,
        # which still means a potentially expensive join if not indexed well.
        query = query.filter(UserChatMap.user_id.ilike(f"%{search_text}%"))
        # If you absolutely must search content snippets at this stage (less optimal):
        # query = query.join(ChatHistory, UserChatMap.session_id == ChatHistory.session_id)\
        #              .filter(ChatHistory.message.ilike(f'%"{search_text}"%')) # Crude JSON search

    now = datetime.now()  # Use aware or naive datetimes consistently
    if date_filter == "today":
        today_start = datetime(now.year, now.month, now.day)
        query = query.filter(subquery.c.last_update >= today_start)
    elif date_filter == "week":
        week_start = now - timedelta(days=now.weekday())
        week_start = datetime(week_start.year, week_start.month, week_start.day)
        query = query.filter(subquery.c.last_update >= week_start)
    elif date_filter == "month":
        month_start = datetime(now.year, now.month, 1)
        query = query.filter(subquery.c.last_update >= month_start)

    # Apply message count filter (using HAVING on the aggregated count)
    # This requires the grouping to happen in the main query or filtering on the subquery result
    if message_count_filter == "short":  # Assuming short: <= 10 messages (5 human, 5 AI)
        query = query.filter(subquery.c.message_count <= 10)
    elif message_count_filter == "medium":  # Assuming medium: 11-40 messages
        query = query.filter(subquery.c.message_count > 10, subquery.c.message_count <= 40)
    elif message_count_filter == "long":  # Assuming long: > 40 messages
        query = query.filter(subquery.c.message_count > 40)

    # Get total count for pagination (before applying limit/offset for items)
    total_items = query.count()

    # Apply ordering and pagination
    sessions_data = query.order_by(desc(subquery.c.last_update)) \
        .offset((page - 1) * page_size) \
        .limit(page_size) \
        .all()

    chat_summaries = []
    for user_id, session_id, last_update, message_count in sessions_data:
        chat_summaries.append({
            "user_id": user_id,
            "session_id": session_id,  # Crucial for fetching details later
            "last_update": last_update.isoformat() if last_update else None,
            # Frontend was doing Math.floor(length / 2), so if message_count is total messages
            # we can pass it directly, or adjust if it represents something else.
            "message_interaction_count": message_count // 2 if message_count else 0,  # Example for "turns"
            "total_raw_message_count": message_count if message_count else 0
        })

    return {
        "items": chat_summaries,
        "total": total_items,
        "page": page,
        "page_size": page_size,
        "pages": (total_items + page_size - 1) // page_size if total_items > 0 else 0
    }


@chat_history.get("/api/sessions/{session_id}/messages", response_class=JSONResponse)
async def get_session_messages(
        session_id: str,
        db: Session = Depends(get_db)
):
    """获取特定会话的所有消息"""
    logger.info(f"获取会话 {session_id} 的消息")

    messages_query = db.query(
        ChatHistory.message,
        ChatHistory.created_at
    ).filter(
        ChatHistory.session_id == session_id
    ).order_by(ChatHistory.created_at).all()

    if not messages_query:
        raise HTTPException(status_code=404, detail="Session not found or has no messages")

    processed_messages = []
    for message, created_at in messages_query:
        try:
            if isinstance(message, dict):
                message_data = message
            else:
                message_data = json.loads(message)  # Ensure message is a string before loads

            message_type = message_data.get('type', 'unknown')
            content = ''
            content_data = message_data.get('data', {})
            if message_type in ['human', 'ai']:
                content = content_data.get('content', '')
            elif message_type == 'unknown' and isinstance(message,
                                                          str):  # Handle case where original message was a simple string
                content = f"无法解析的消息: {message[:100]}..."

            processed_messages.append({
                "type": message_type,
                "content": content,
                "timestamp": created_at.isoformat()
            })
        except (json.JSONDecodeError, AttributeError, KeyError, TypeError) as e:
            logger.error(f"Error parsing message in session {session_id}: {e}. Message: {str(message)[:200]}")
            processed_messages.append({
                "type": "error",
                "content": f"无法解析的消息: {str(message)[:100]}...",
                "timestamp": created_at.isoformat()
            })
    return processed_messages


# The endpoint `/api/sessions/{user_id}` might still be useful for other purposes,
# but for displaying chat history, the per-session message loading is better.
# You can keep it or deprecate it based on your needs.
# If you keep it, ensure it's also optimized or clearly documented for its specific use case.

@chat_history.get("/api/users/{user_id}/sessions", response_class=JSONResponse)
async def get_user_sessions_summary(  # Renamed for clarity, or you can adjust existing
        user_id: str,
        db: Session = Depends(get_db)
):
    """获取特定用户的所有会话摘要 (ID 和最后更新时间)"""
    logger.info(f"获取用户 {user_id} 的会话摘要")
    user_sessions_query = db.query(
        UserChatMap.session_id,
        func.max(ChatHistory.created_at).label("last_session_activity")
    ).join(
        ChatHistory, UserChatMap.session_id == ChatHistory.session_id
    ).filter(
        UserChatMap.user_id == user_id,
        UserChatMap.is_deleted == "f"
    ).group_by(
        UserChatMap.session_id
    ).order_by(
        desc("last_session_activity")
    ).all()

    if not user_sessions_query:
        return []

    result = [
        {
            "session_id": session.session_id,
            "last_update": session.last_session_activity.isoformat() if session.last_session_activity else None,
        }
        for session in user_sessions_query
    ]
    return result
