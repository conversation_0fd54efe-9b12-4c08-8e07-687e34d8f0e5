import logging
import math
import os
import uuid
from typing import Any, Sequence

import matplotlib.pyplot as plt
from fastapi.responses import JSONResponse
from matplotlib.font_manager import FontProperties
from sqlalchemy import create_engine, URL, text, Row
from sqlalchemy.exc import SQLAlchemyError

from core.config.logging import logger

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 方法二：如果上面方法不生效，可以尝试下面的字体管理器方法
try:
    # 尝试加载中文字体
    font = FontProperties(family='SimHei')
except:
    # 如果找不到SimHei字体，打印警告并继续
    print("警告：找不到SimHei字体，图表中文可能显示为乱码")
    font = None

from fastapi import APIRouter, Query, HTTPException

slot_recommendations_router = APIRouter(prefix="/api/v2/dify_tools/slot_recommendations", tags=["Dify工具集"])


@slot_recommendations_router.get("/recommendations")
def get_slot_recommendations(
        client_name: str = Query(..., description="客户名称，例如 曲靖阳光"),
        wire_shape: str = Query(..., description="线型，例如 24"),
        stand_thickness: str = Query(..., description="台厚，例如 130")
):
    """
    根据客户名称、线型和台厚获取槽型推荐信息。

    参数:
    - client_name (str): 客户名称。
    - wire_shape (str): 线型。
    - stand_thickness (str): 台厚。

    返回:
    - JSONResponse: 包含推荐信息的JSON响应。
    """
    try:
        info = fetch_slot_recommendations(client_name, wire_shape, stand_thickness)
        if info:
            slot_rate = info[0][0]
            total = sum(slot_rate.values())
            slot_rate_percentages = {k: f"{(v / total * 100):.2f}%" for k, v in slot_rate.items()}
            fig1 = plot_wire_diameter_pie(info[0][2], wire_shape, client_name)
            fig2 = plot_data(info[0][5])
            return JSONResponse(
                status_code=200,
                content={
                    "flag": "success",
                    "slot_rate_percentages": '\n'.join([f"{k}：{v}" for k, v in slot_rate_percentages.items()]),
                    "fig1": fig1,
                    "fig2": fig2
                }
            )

        else:
            return JSONResponse(
                status_code=204,
                content=None
            )

    except Exception as e:
        import traceback
        logger.info(f"{str(e)}")
        logger.info(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"处理数据失败: {str(e)}")


@slot_recommendations_router.get("/get_all_spec")
def get_all_spec():
    """
    从数据库中获取所有推荐的槽规格信息，并以格式化的字符串形式返回。

    :return: 包含所有推荐槽规格信息的JSON响应，如果成功，还包括状态码200和成功标志；
             如果处理数据失败，则返回状态码500和错误信息。
    """
    out = []
    try:
        engine = get_db_connection()
        with engine.connect() as connection:
            result = connection.execute(text("""
                SELECT client_name, wire_shape, stand_thickness FROM "slot_recommend" ORDER BY "end_time"
            """)).fetchall()
        for r in result:
            row_tuple = tuple(r)
            if row_tuple not in out:
                out.append(row_tuple)

        recommend_format_string = "\n".join([
            f"客户名称：{client}；线径：{wire_shape}；标准片厚：{int(thickness)}"
            for client, wire_shape, thickness in out
        ])

        return JSONResponse(
            status_code=200,
            content={
                "flag": "success",
                "result": recommend_format_string
            }
        )
    except Exception as e:
        import traceback
        logger.info(f"{str(e)}")
        logger.info(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"处理数据失败: {str(e)}")


@slot_recommendations_router.get("/get_same_thinness_diff")
def get_same_thinness_diff(
        wire_shape: str = Query(..., description="线型，例如 24"),
        stand_thickness: str = Query(..., description="台厚，例如 130"),
        query: str = Query(..., description="用户输入：偏厚or偏薄")
):
    """
    根据线型、台厚和用户查询偏好，获取相同薄度但不同尺寸的槽线推荐。

    参数:
    - wire_shape: str, 线型标识，例如 "24"
    - stand_thickness: str, 台厚标识，例如 "130"
    - query: str, 用户查询偏好，可以是 "偏厚" 或 "偏薄"

    返回:
    - JSONResponse: 包含槽线比率百分比的JSON响应，如果无数据则返回空响应
    """
    try:
        info = fetch_same_thinness_diff_data(query, wire_shape, stand_thickness)
        if info:
            out = {}
            for r in info:
                slot_rate = r[0]
                total = sum(slot_rate.values())
                slot_rate_percentages = {k: f"{(v / total * 100):.2f}%" for k, v in slot_rate.items()}
                out[r[6]] = slot_rate_percentages
                # fig1 = plot_wire_diameter_data(r[0][2])
                # fig2 = plot_data(r[0][5])
            return JSONResponse(
                status_code=200,
                content={
                    "flag": "success",
                    "slot_rate_percentages": '\n'.join([f"{k}：{v}" for k, v in out.items()]),
                    # "fig1": fig1,
                    # "fig2": fig2
                }
            )

        else:
            return JSONResponse(
                status_code=204,
                content=None
            )

    except Exception as e:
        import traceback
        logger.info(f"{str(e)}")
        logger.info(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"处理数据失败: {str(e)}")


def get_db_connection():
    """Create and return database connection"""
    url = URL.create(
        drivername="postgresql+psycopg2",
        username="postgres",
        password="YB@PGCluster_1223",  # 原始密码，无需编码
        host="***********",
        port=5432,
        database="gc_iotcut"
    )

    engine = create_engine(url)
    return engine


def fetch_same_thinness_diff_data(query: str, wire_shape: str, stand_thickness: str) -> Sequence[Row[Any]] | None:
    engine = get_db_connection()

    # Using parameterized query to prevent SQL injection
    if "偏厚" in query:
        query = """
                SELECT slot_rate, tinkness_diff, group_line_prop, start_time, end_time, plot_data, client_name
                FROM slot_recommend
                WHERE CAST(tinkness_diff AS numeric) < 0
                  AND wire_shape = :wire_shape
                  AND stand_thickness LIKE :stand_thickness
                ORDER BY end_time DESC;
                LIMIT 3;
        """
    # AND end_time = (SELECT MAX(end_time) FROM slot_recommend)
    elif "偏薄" in query:
        query = """
                SELECT slot_rate, tinkness_diff, group_line_prop, start_time, end_time, plot_data, client_name
                FROM slot_recommend
                WHERE CAST(tinkness_diff AS numeric) > 0
                  AND wire_shape = :wire_shape
                  AND stand_thickness LIKE :stand_thickness
                ORDER BY end_time DESC
                LIMIT 3;
        """
    else:
        pass
    params = {
        'wire_shape': wire_shape,
        'stand_thickness': f'%{stand_thickness}%'
    }

    try:
        with engine.connect() as connection:
            result = connection.execute(text(query), params).fetchall()
            return result
    except SQLAlchemyError as e:
        logging.error(f"Database error: {e}")
        return None
    except UnicodeDecodeError as e:
        logging.error(f"Unicode decoding error: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return None
    finally:
        engine.dispose()


def fetch_slot_recommendations(client_name: str, wire_shape: str, stand_thickness: str) -> Sequence[Row[Any]] | None:
    """
    Get slot rate data from PostgreSQL database.

    Args:
        client_name: Client name to search for
        wire_shape: Shape of the wire
        stand_thickness: Thickness of the stand

    Returns:
        Tuple containing slot_rate, thickness_diff, group_line_prop,
        start_time, end_time, plot_data or None if error occurs
    """
    # Input validation
    if not client_name or not isinstance(client_name, str):
        logging.error("Invalid client_name parameter")
        return None

    engine = get_db_connection()

    # Using parameterized query to prevent SQL injection
    query = """
        SELECT slot_rate, tinkness_diff, group_line_prop, start_time, end_time, plot_data
        FROM slot_recommend 
        WHERE client_name LIKE :client_name
        AND wire_shape = :wire_shape
        AND stand_thickness = :stand_thickness
        ORDER BY end_time DESC
    """
    params = {
        'client_name': f"%{client_name}%",
        'wire_shape': wire_shape,
        'stand_thickness': stand_thickness
    }

    try:
        with engine.connect() as connection:
            result = connection.execute(text(query), params).fetchall()
            return result
    except SQLAlchemyError as e:
        logging.error(f"Database error: {e}")
        return None
    except UnicodeDecodeError as e:
        logging.error(f"Unicode decoding error: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return None
    finally:
        engine.dispose()


def plot_wire_diameter_pie(data, wire_shape, client_name):
    """
    根据线径区间钢线辊数数据绘制饼图。

    参数:
    data (list): 包含线径段和唯一线路代码计数的字典列表。
    wire_shape (str): 线型
    client_name (str): 客户名称
    font: 字体（如需显示中文）
    """
    # 1. 数据处理：区间向下、向上取整，统计每个区间的钢线辊数
    slot_wire_bucket = {}
    for item in data:
        if item["unique_line_code_count"] == 0:
            continue
        left_floor = math.floor(item["wire_diameter_segment"]["left"])
        right_ceil = math.ceil(item["wire_diameter_segment"]["right"])
        concat_key = f"({left_floor},{right_ceil}]"
        if concat_key not in slot_wire_bucket:
            slot_wire_bucket[concat_key] = {"unique_line_code_count": item["unique_line_code_count"]}
        else:
            slot_wire_bucket[concat_key]["unique_line_code_count"] += item["unique_line_code_count"]

    # 2. 构建饼图所需数据
    labels = []
    counts = []
    for k, v in slot_wire_bucket.items():
        labels.append(k)
        counts.append(v["unique_line_code_count"])

    # 3. 绘制饼图
    plt.figure(figsize=(9, 8))
    patches, texts, autotexts = plt.pie(
        counts,
        labels=labels,
        autopct='%1.1f%%',
        startangle=140,
        textprops={'fontproperties': font} if font else None
    )

    # 字体设置
    if font:
        for t in texts + autotexts:
            t.set_fontproperties(font)

    # 中文标题和图例
    plt.title(f'{wire_shape}线-钢线外径分布', fontproperties=font if font else None, fontsize=14)
    plt.legend(labels, loc='best', title="外径范围", prop=font if font else None)
    plt.axis('equal')  # 保证是圆形

    plt.tight_layout()

    total_count = sum(counts)
    # 获取当前坐标轴对象
    ax = plt.gca()
    x_min, x_max = ax.get_xlim()
    y_min, y_max = ax.get_ylim()

    # 在左上角加文字，略微内移避免贴边
    plt.text(
        x_min + 0.05 * (x_max - x_min),  # x坐标略微偏移
        y_max - 0.05 * (y_max - y_min),  # y坐标略微偏移
        f"金刚线卷数: {total_count}",
        fontsize=14,
        ha='left', va='top',
        fontproperties=font if font else None
    )

    # 4. 保存图片
    BaseDir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
    static_dir = os.path.join(BaseDir, 'static', 'slot_recommendations')
    os.makedirs(static_dir, exist_ok=True)

    img_name = f"wire_diameter_pie_{uuid.uuid4().hex}.png"
    img_path = os.path.join(static_dir, img_name)
    plt.savefig(img_path)
    plt.close()

    return f"http://**********:59990/static/slot_recommendations/{img_name}"


def plot_data(data):
    slot_wire_bucket = {}
    for item in data:
        slot_distance = item["槽距"]
        left_floor = math.floor(item["wire_diameter_segment"]["left"])
        right_ceil = math.ceil(item["wire_diameter_segment"]["right"])
        concat_key = f"{slot_distance}-({left_floor},{right_ceil}]"
        weight = item["knife_sn"]
        if concat_key not in slot_wire_bucket:
            slot_wire_bucket[concat_key] = {"count": weight, "ph": item["ph±3"] * weight,
                                            "thickness": item["片厚：均值(A)"] * weight}
        else:
            slot_wire_bucket[concat_key]["count"] += weight
            slot_wire_bucket[concat_key]["ph"] += item["ph±3"] * weight
            slot_wire_bucket[concat_key]["thickness"] += item["片厚：均值(A)"] * weight
    # 求平均
    for key, value in slot_wire_bucket.items():
        slot_wire_bucket[key]["ph"] = round(slot_wire_bucket[key]["ph"] / value["count"], 2)
        slot_wire_bucket[key]["thickness"] = round(slot_wire_bucket[key]["thickness"] / value["count"], 2)
        del slot_wire_bucket[key]['count']

    keys = list(slot_wire_bucket.keys())
    thickness = [slot_wire_bucket[k]['thickness'] for k in keys]
    ph = [slot_wire_bucket[k]['ph'] for k in keys]
    total_knife = sum([x["knife_sn"] for x in data])

    fig, ax1 = plt.subplots(figsize=(10, 6))

    # 设置 thickness 柱状图
    bars = ax1.bar(keys, thickness, color='skyblue', label=f"{data[0]['client_name']}: {total_knife}刀")
    ax1.set_ylabel('平均厚度')
    ax1.set_ylim(120, 138)
    ax1.set_xlabel('槽距线径组合')
    ax1.tick_params(axis='x', rotation=45)

    # 给每个柱子标注数值
    for bar, value in zip(bars, thickness):
        ax1.text(bar.get_x() + bar.get_width() / 2, value + 0.2, f'{value:.2f}',
                 ha='center', va='bottom', fontsize=9)

    # 设置 pH 折线图
    ax2 = ax1.twinx()
    line = ax2.plot(keys, ph, color='red', marker='o', label=data[0]['client_name'])
    ax2.set_ylabel('片厚±3异常率')
    ax2.set_ylim(0, 12)

    # 给每个点标注 pH 数值
    for x, y in zip(keys, ph):
        ax2.text(x, y + 0.3, f'{y:.2f}', ha='center', va='bottom', fontsize=9, color='red')

    # 添加图例
    ax1.legend(loc='upper left')
    ax2.legend(loc='upper right')

    plt.title('不同线径-槽距组合对应的平均片厚、片厚±3异常率')
    plt.tight_layout()

    # 保存到 static/slot_recommendations 目录，确保目录存在
    BaseDir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
    static_dir = os.path.join(BaseDir, 'static', 'slot_recommendations')
    os.makedirs(static_dir, exist_ok=True)

    img_name = f"wire_diameter_plot_{uuid.uuid4().hex}.png"
    img_path = os.path.join(static_dir, img_name)

    plt.savefig(img_path)
    plt.close()

    return f"http://**********:59990/static/slot_recommendations/{img_name}"


if __name__ == '__main__':
    data = [{"client_name":"四川兴德利新能源有限公司","槽距":169.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":36.0,"mid":36.1665,"open_right":False,"right":36.333},"ph±3":1.2443902439,"knife_sn":41,"片厚：均值(A)":130.3080255463,"line_diameter":36.1895121951,"标准片厚":130.0,"线型":"24","wire_mid":36.1665,"client_encoded":0},{"client_name":"四川兴德利新能源有限公司","槽距":169.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":36.333,"mid":36.5,"open_right":False,"right":36.667},"ph±3":1.768125,"knife_sn":32,"片厚：均值(A)":129.7972893297,"line_diameter":36.530625,"标准片厚":130.0,"线型":"24","wire_mid":36.5,"client_encoded":0},{"client_name":"四川兴德利新能源有限公司","槽距":169.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":36.667,"mid":36.8335,"open_right":False,"right":37.0},"ph±3":1.6361764706,"knife_sn":34,"片厚：均值(A)":129.9726935283,"line_diameter":36.8647058824,"标准片厚":130.0,"线型":"24","wire_mid":36.8335,"client_encoded":0},{"client_name":"四川兴德利新能源有限公司","槽距":170.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":36.333,"mid":36.5,"open_right":False,"right":36.667},"ph±3":3.1828070175,"knife_sn":57,"片厚：均值(A)":131.0112990753,"line_diameter":36.4896491228,"标准片厚":130.0,"线型":"24","wire_mid":36.5,"client_encoded":0},{"client_name":"四川兴德利新能源有限公司","槽距":170.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":36.667,"mid":36.8335,"open_right":False,"right":37.0},"ph±3":1.8185388128,"knife_sn":219,"片厚：均值(A)":130.7220041179,"line_diameter":36.84456621,"标准片厚":130.0,"线型":"24","wire_mid":36.8335,"client_encoded":0},{"client_name":"四川兴德利新能源有限公司","槽距":170.0,"wire_diameter_segment":{"closed":"right","closed_right":True,"left":37.0,"mid":37.1665,"open_right":False,"right":37.333},"ph±3":0.2968,"knife_sn":25,"片厚：均值(A)":130.646860625,"line_diameter":37.1152,"标准片厚":130.0,"线型":"24","wire_mid":37.1665,"client_encoded":0}]
    plot_data(data)
    # get_all_spec()
