from uuid import uuid4

from pydantic import BaseModel, Field

from typing import Optional


class QueryRequest(BaseModel):
    messages: str = Field(..., description="用户输入的问题")
    uuid: str = Field(..., description="会话ID")
    think: bool = Field(False, description="是否启用深度思考")
    userId: str = Field(..., description="用户工号")

    assistant_number: int = Field(..., description="助手编号：1-知识库检索，2-数据检索，3-项目管理BI数据检索，"
                                                   "4-AGV异常排查助手，5-槽距推荐助手，6-项目模板下载助手，"
                                                   "7-切片人员评估助手，8-会议纪要助手")


class SliceMchQueryRequest(BaseModel):
    label: str = Field(..., description="问题")
    value: str = Field(..., description="问题表示")
    userId: str = Field(..., description="工号")
    machineNo: str = Field(..., description="机台号")


class UploadRequest(BaseModel):
    userId: str = Field(..., description="用户工号")
    fileUrl: str = Field(..., description="文件访问地址")
    fileId: int = Field(..., description="文件唯一标识")
    filName: str = Field(..., description="文件名称")
    domain: str = Field(..., description="文件所属领域")


class UserChatResponse(BaseModel):
    uuid: uuid4 = Field(..., description="会话ID")
    desc: str = Field(..., description="描述信息")

