from fastapi import APIRouter

from api.endpoints.admin.bi_train_data_contorller import bi_training_router
from api.endpoints.admin.check_chat_history_contorller import chat_history
from api.endpoints.admin.home_contorller import home_router
from api.endpoints.admin.knowledge_mange_controller import upload_knowledge_router
from api.endpoints.admin.text2sql_train_data_contorller import training_router
from api.endpoints.analytics import statistics_router
from api.endpoints.conversations import conversations_router
from api.endpoints.dify_tools.slot_recommendations import slot_recommendations_router
from api.endpoints.dify_tools.agv_abnormal import tcx_status_router
from api.endpoints.files import file_router

router = APIRouter()

# 注册子路由器
router.include_router(conversations_router)
router.include_router(file_router)
router.include_router(upload_knowledge_router)
router.include_router(training_router)
router.include_router(bi_training_router)
router.include_router(home_router)
router.include_router(chat_history)
router.include_router(tcx_status_router)
router.include_router(slot_recommendations_router)
router.include_router(statistics_router)