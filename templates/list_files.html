<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>向量数据库文件列表[后台]</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 20px;
            padding: 20px;
        }
        h2 {
            color: #333;
            text-align: center;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
            align-items: center;
        }
        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
            font-size: 16px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0069d9;
        }
        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 1200px;
            margin: auto;
        }
        .category-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 15px;
            transition: transform 0.2s ease-in-out;
        }
        .category-card:hover {
            transform: translateY(-5px);
        }
        .category-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #007bff;
            text-align: center;
        }
        .file-list {
            list-style-type: none;
            padding: 0;
        }
        .file-list li {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-list li:hover {
            background-color: #f1f1f1;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .empty-message {
            text-align: center;
            padding: 20px;
            font-size: 16px;
            color: #666;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 16px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h2>向量数据库文件列表</h2>

    <div class="controls">
        <label for="collection-select">选择集合：</label>
        <select id="collection-select">
            <!-- 动态加载集合选项 -->
        </select>
        <button id="fetch-btn">获取文件列表</button>
    </div>

    <div class="loading" id="loading-indicator" style="display: none;">正在加载数据，请稍候...</div>
    <div class="container" id="file-container"></div>

    <script>
        // 获取文件列表
        function fetchFileList() {
            const collectionName = document.getElementById('collection-select').value;
            const loadingIndicator = document.getElementById('loading-indicator');
            const fileContainer = document.getElementById('file-container');

            // 显示加载指示器
            loadingIndicator.style.display = 'block';
            fileContainer.innerHTML = "";

            fetch(`/knowledge_mange/list-files?collection_name=${encodeURIComponent(collectionName)}`)
            .then(response => response.json())
            .then(data => {
                // 隐藏加载指示器
                loadingIndicator.style.display = 'none';

                if (data.success) {
                    if (Object.keys(data.data).length === 0) {
                        // 没有数据时显示提示
                        fileContainer.innerHTML = '<div class="empty-message">没有找到文件数据</div>';
                        return;
                    }

                    // 清空旧内容
                    fileContainer.innerHTML = "";

                    for (const category in data.data) {
                        const card = document.createElement('div');
                        card.classList.add('category-card');

                        const title = document.createElement('div');
                        title.classList.add('category-title');
                        title.textContent = category;

                        const ul = document.createElement('ul');
                        ul.classList.add('file-list');

                        data.data[category].forEach(fileName => {
                            const li = document.createElement('li');
                            li.textContent = fileName;

                            // 创建删除按钮
                            const deleteBtn = document.createElement('button');
                            deleteBtn.classList.add('delete-btn');
                            deleteBtn.textContent = "删除";
                            deleteBtn.onclick = function() {
                                deleteFile(fileName, collectionName);
                            };

                            li.appendChild(deleteBtn);
                            ul.appendChild(li);
                        });

                        card.appendChild(title);
                        card.appendChild(ul);
                        fileContainer.appendChild(card);
                    }
                } else {
                    console.error("获取文件列表失败:", data.error);
                    fileContainer.innerHTML = `<div class="empty-message">获取文件列表失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error("请求失败:", error);
                loadingIndicator.style.display = 'none';
                fileContainer.innerHTML = '<div class="empty-message">请求失败，请检查网络连接</div>';
            });
        }

        // 删除文件
        function deleteFile(fileName, collectionName) {
            if (confirm(`确定要删除文件 "${fileName}" 吗？`)) {
                fetch(`/knowledge_mange/delete-file?file_name=${encodeURIComponent(fileName)}&collection_name=${encodeURIComponent(collectionName)}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`文件 "${fileName}" 已删除！`);
                        fetchFileList(); // 重新获取文件列表
                    } else {
                        alert("删除失败：" + data.error);
                    }
                })
                .catch(error => console.error("删除请求失败:", error));
            }
        }

        // 获取集合列表
            function fetchCollections() {
                const collectionSelect = document.getElementById('collection-select');
                const loadingIndicator = document.getElementById('loading-indicator');

                // 显示加载指示器
                loadingIndicator.style.display = 'block';

                fetch('/knowledge_mange/list-collections')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 清空旧选项
                        collectionSelect.innerHTML = "";

                        // 添加新选项
                        data.data.forEach(collection => {
                            const option = document.createElement('option');
                            option.value = collection;
                            option.textContent = collection;
                            collectionSelect.appendChild(option);
                        });

                        // 如果有集合，则获取第一个集合的文件列表
                        if (data.data.length > 0) {
                            fetchFileList();
                        }
                    } else {
                        console.error("获取集合列表失败:", data.error);
                        alert("获取集合列表失败: " + data.error);
                    }

                    // 隐藏加载指示器
                    loadingIndicator.style.display = 'none';
                })
                .catch(error => {
                    console.error("请求失败:", error);
                    loadingIndicator.style.display = 'none';
                    alert("请求失败，请检查网络连接");
                });
            }

        // 添加事件监听器
        document.getElementById('fetch-btn').addEventListener('click', fetchFileList);

        // 也可以添加下拉菜单变化时自动获取
        document.getElementById('collection-select').addEventListener('change', fetchFileList);

        // 页面加载时获取文件列表
        window.onload = fetchCollections;
    </script>
</body>
</html>