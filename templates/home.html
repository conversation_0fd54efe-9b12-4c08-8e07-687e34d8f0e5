<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #4cc9f0;
            --info-color: #4895ef;
            --warning-color: #f72585;
            --danger-color: #ff006e;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --blue-color: #4cc9f0;
            --green-color: #06d6a0;
            --red-color: #ef476f;
            --orange-color: #ffd166;
            --purple-color: #7209b7;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .site-title {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .header-nav a {
            color: white;
            text-decoration: none;
            margin-left: 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .header-nav a:hover {
            opacity: 0.8;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .welcome-section h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .welcome-section p {
            font-size: 1.1rem;
            color: #666;
            max-width: 700px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .service-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .service-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .service-title h2 {
            font-size: 1.5rem;
            margin-bottom: 0.3rem;
        }

        .service-title p {
            color: #666;
            font-size: 0.9rem;
        }

        .service-links {
            padding: 0 1.5rem 1.5rem;
        }

        .service-link {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 6px;
            text-decoration: none;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            transition: background-color 0.3s ease;
        }

        .service-link:hover {
            background-color: #f5f7fa;
        }

        .service-link:last-child {
            margin-bottom: 0;
        }

        .service-link i {
            margin-right: 0.8rem;
            width: 20px;
            text-align: center;
        }

        footer {
            background-color: var(--dark-color);
            color: var(--light-color);
            padding: 2rem 0;
            margin-top: 2rem;
        }

        footer .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-info p {
            margin: 0;
            opacity: 0.8;
        }

        .color-blue { background-color: var(--blue-color); }
        .color-green { background-color: var(--green-color); }
        .color-red { background-color: var(--red-color); }
        .color-orange { background-color: var(--orange-color); }
        .color-purple { background-color: var(--purple-color); }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            header .container {
                flex-direction: column;
                text-align: center;
            }
            
            .header-nav {
                margin-top: 1rem;
            }
            
            .header-nav a {
                margin: 0 0.5rem;
            }
            
            footer .container {
                flex-direction: column;
                text-align: center;
            }
            
            .footer-info {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="site-title">GC-LLM后台数据管理</div>
            <nav class="header-nav">
                <a href="/"><i class="fas fa-home"></i> 首页</a>
                <a href="#services"><i class="fas fa-th-large"></i> 服务</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <section class="welcome-section">
            <h1>欢迎使用后台数据管理平台</h1>
            <p>集中管理了所有后端服务，通过下方的卡片导航到不同的功能模块。</p>
        </section>

        <section id="services" class="services-section">
            <div class="services-grid">
                {% for service in services %}
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon color-{{ service.color }}">
                            <i class="fas fa-{{ service.icon }}"></i>
                        </div>
                        <div class="service-title">
                            <h2>{{ service.name }}</h2>
                            <p>{{ service.description }}</p>
                        </div>
                    </div>
                    <div class="service-links">
                        {% for link in service.links %}
                        <a href="{{ link.url }}" class="service-link">
                            <i class="fas fa-{{ link.icon }}"></i>
                            <span>{{ link.name }}</span>
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}

            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <div class="footer-info">
                <p>系统服务中心-研究二室 &copy; <span id="current-year"></span></p>
            </div>
            <div class="footer-links">
                <a href="#" style="color: white; margin-right: 15px;"><i class="fas fa-question-circle"></i> 帮助</a>
                <a href="#" style="color: white;"><i class="fas fa-info-circle"></i> 关于</a>
            </div>
        </div>
    </footer>

    <script>
        // 设置当前年份
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
</body>
</html>