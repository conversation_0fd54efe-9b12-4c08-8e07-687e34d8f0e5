<!DOCTYPE html>
<html>
<head>
    <title>BI训练数据管理</title>
    <style>
body {
    font-family: 'Segoe UI', Arial, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    background: #f4f8fb;
}

h1, h2 {
    color: #222a35;
}

.success-message {
    background: linear-gradient(90deg, #e9fbe5 60%, #e0ffe0 100%);
    color: #2d783a;
    padding: 12px 24px;
    margin-bottom: 24px;
    border-left: 5px solid #40c765;
    border-radius: 6px;
    font-size: 16px;
}

.form-section {
    background: #fff;
    margin-bottom: 32px;
    padding: 28px 24px 18px;
    border-radius: 12px;
    box-shadow: 0 3px 12px rgba(30,77,100,0.07);
    border: none;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #32546d;
}

textarea {
    width: 100%;
    min-height: 90px;
    resize: vertical;
    margin-bottom: 14px;
    padding: 10px;
    border-radius: 7px;
    border: 1px solid #dde2ec;
    font-size: 14px;
    background: #f7fafd;
    transition: border 0.2s;
}
textarea:focus {
    outline: none;
    border-color: #258be4;
    background: #fff;
}

input[type="submit"] {
    background: linear-gradient(90deg,#40bcef 80%,#2bafd9 100%);
    color: #fff;
    padding: 10px 26px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 15px;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 6px rgba(64,188,239,0.07);
}
input[type="submit"]:hover {
    background: #258be4;
}

.tabs {
    border: none;
    background: none;
    margin-bottom: 32px;
    display: flex;
    gap: 6px;
}

.tab-button {
    font-size: 16px;
    background: #f5f7fa;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 12px 32px;
    border-radius: 8px 8px 0 0;
    color: #32546d;
    margin-right: 4px;
    box-shadow: 0 2px 6px rgba(193,208,225,0.07);
    transition: background 0.2s, color 0.2s;
}
.tab-button.active, .tab-button:hover {
    background: #fff;
    color: #2196f3;
    font-weight: bold;
}

.tab-content {
    display: none;
    padding: 14px 0 0 0;
    border: none;
    background: none;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 4px;
    background: none;
}

th, td {
    padding: 10px;
    background: #fff;
    border: none;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(180,200,220,0.05);
    vertical-align: top;
    font-size: 15px;
}

th {
    background: #f5f7fa;
    font-weight: 600;
    color: #32546d;
    border-bottom: 2px solid #e8f0fb;
}

tr:not(:last-child) td {
    border-bottom: 1px solid #e8f0fb;
}

tr:nth-child(even) td {
    background: #f9fafb;
}

.data-content {
    max-height: 120px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Fira Mono', 'Courier', monospace;
    font-size: 13px;
    color: #444;
}

.sql-content {
    font-family: 'Fira Mono', 'Courier', monospace;
    font-size: 13px;
    white-space: pre-wrap;
}

.show-link {
    color: #2196f3;
    cursor: pointer;
    text-decoration: underline;
}

.delete-form {
    margin: 0;
}

.delete-button {
    background: #e05353;
    color: #fff;
    border: none;
    padding: 6px 18px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}
.delete-button:hover {
    background: #b93434;
}

@media (max-width: 700px) {
    .form-section, th, td {
        padding-left: 7px;
        padding-right: 7px;
    }
    table, thead, tbody, th, td, tr {
        display: block;
    }
    th, td {
        width: 100%;
    }
    tr {
        margin-bottom: 10px;
    }
    .tabs {
        flex-direction: column;
    }
}

.filter-container {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-container label {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 0;
    }

    #type-filter {
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #dde2ec;
        background-color: #f7fafd;
        color: #32546d;
        font-size: 14px;
        min-width: 120px;
    }

    #type-filter:focus {
        outline: none;
        border-color: #258be4;
        background: #fff;
    }
    </style>
</head>
<body>
    <h1>训练数据管理</h1>

    {% if success_message %}
    <div class="success-message">
        {{ success_message }}
    </div>
    {% endif %}

    <div class="tabs">
        <button class="tab-button active" onclick="openTab(event, 'ddl-tab')">添加DDL训练数据</button>
        <button class="tab-button" onclick="openTab(event, 'qa-tab')">添加问题和SQL训练数据</button>
        <button class="tab-button" onclick="openTab(event, 'view-tab')">查看训练数据</button>
    </div>

<div id="ddl-tab" class="tab-content" style="display: block;">
    <div class="form-section">
        <h2>添加DDL训练数据</h2>
        <form action="/training/bi/add" method="post">
            <label for="ddl">DDL内容:</label>
            <textarea id="ddl" name="ddl" required></textarea>

            <label for="documents_summary">文档摘要:</label>
            <textarea id="documents_summary" name="documents_summary" required></textarea>

            <label for="table_name">表名:</label>
            <input type="text" id="table_name" name="table_name" required />

            <input type="submit" value="添加">
        </form>
    </div>
</div>


    <div id="qa-tab" class="tab-content">
        <div class="form-section">
            <h2>添加问题和SQL训练数据</h2>
            <form action="/training/bi/add" method="post">
                <label for="question">问题:</label>
                <textarea id="question" name="question" required></textarea>
                <label for="sql">SQL:</label>
                <textarea id="sql" name="sql" required></textarea>
                <input type="submit" value="添加">
            </form>
        </div>
    </div>

    <div id="view-tab" class="tab-content">
        <div class="form-section">
            <h2>已有训练数据</h2>
            <!-- 添加筛选框 -->
        <div class="filter-container">
            <label for="type-filter">筛选类型:</label>
            <select id="type-filter" onchange="filterTrainingData()">
                <option value="all">全部</option>
                <option value="sql">SQL</option>
                <option value="ddl">DDL</option>
                <option value="documentation">Documentation</option>
            </select>
        </div>

        <table id="training-data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>类型</th>
                    <th>内容</th>
                    <th>SQL</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for index, row in training_data.iterrows() %}
                <tr data-type="{{ row.training_data_type }}">
                    <td>{{ row.id }}</td>
                    <td>{{ row.training_data_type }}</td>
                    <td>
                        <div class="data-content">
                            {% if row.question %}
                                问题: {{ row.question }}
                            {% else %}
                                {{ row.content }}
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <div class="sql-content">
                            <div class="preview">{{ row.content[:100] }}{% if row.content|length > 100 %}...<span class="show-link" onclick="toggleSql(this)">展开</span>{% endif %}</div>
                            <div class="full-content" style="display: none;">{{ row.content }}<span class="show-link" onclick="toggleSql(this)">收起</span></div>
                        </div>
                    </td>
                    <td>
                        <form class="delete-form" action="/training/bi/remove/{{ row.id }}" method="post">
                            <input type="submit" class="delete-button" value="删除">
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
    <script>
        // 添加筛选功能
    function filterTrainingData() {
        var filter = document.getElementById('type-filter').value;
        var table = document.getElementById('training-data-table');
        var rows = table.getElementsByTagName('tr');

        for (var i = 1; i < rows.length; i++) { // 从1开始跳过表头
            var row = rows[i];
            var type = row.getAttribute('data-type');

            if (filter === 'all' || filter === type) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    }

        function openTab(evt, tabName) {
            var i, tabcontent, tabbuttons;

            // 隐藏所有标签内容
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // 移除所有标签按钮的active类
            tabbuttons = document.getElementsByClassName("tab-button");
            for (i = 0; i < tabbuttons.length; i++) {
                tabbuttons[i].className = tabbuttons[i].className.replace(" active", "");
            }

            // 显示当前标签内容并添加active类到按钮
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        // 添加缺失的toggleSql函数
        function toggleSql(element) {
            var container = element.closest('.sql-content');
            var preview = container.querySelector('.preview');
            var fullContent = container.querySelector('.full-content');

            if (preview.style.display === 'none') {
                preview.style.display = 'block';
                fullContent.style.display = 'none';
            } else {
                preview.style.display = 'none';
                fullContent.style.display = 'block';
            }
        }
    </script>
</body>
</html>
