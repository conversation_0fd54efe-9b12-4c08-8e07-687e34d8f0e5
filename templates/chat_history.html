<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户对话历史</title>
    <style>
        /* ... (keep existing styles) ... */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .user-section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .user-header {
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer; /* Make header clickable to toggle */
        }
        .conversation {
            padding: 20px;
            display: none; /* 默认隐藏对话内容 */
            border-top: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            max-width: 85%;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .human {
            background-color: #e8f4f8;
            align-self: flex-end;
            margin-left: auto;
            border-left: 4px solid #3498db;
        }
        .ai {
            background-color: #f0f0f0; /* Slightly changed for AI */
            align-self: flex-start;
            border-left: 4px solid #2ecc71;
        }
        .error { /* Style for error messages */
            background-color: #ffebee;
            border-left: 4px solid #e53935;
            color: #c62828;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.85em;
            color: #7f8c8d;
        }
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .message-truncated {
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }
        .message-truncated::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            background: linear-gradient(transparent, rgba(240, 240, 240, 0.9));
        }
        .human .message-truncated::after {
            background: linear-gradient(transparent, rgba(232, 244, 248, 0.9));
        }
         .ai .message-truncated::after { /* Ensure AI also has this */
            background: linear-gradient(transparent, rgba(240, 240, 240, 0.9));
        }
        .expand-btn {
            display: block;
            margin: 8px 0 0 0; /* Adjusted margin */
            padding: 4px 10px;
            background-color: #7f8c8d;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            color: white;
            transition: background-color 0.2s;
        }
        .expand-btn:hover {
            background-color: #5e6a6a;
        }
        .toggle-btn { /* This is part of user-header now, not a separate button in DOM initially */
            font-size: 1.2em; /* Make it a bit bigger */
            padding: 0 10px;
        }
        .search-container {
            margin-bottom: 20px;
            text-align: center;
        }
        #searchInput {
            padding: 10px;
            width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        .message-type {
            font-weight: bold;
        }
        .human .message-type {
            color: #3498db;
        }
        .ai .message-type {
            color: #2ecc71;
        }
        .error .message-type { /* Style for error type */
            color: #e53935;
        }
        /* 分页控件样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }
        .pagination button {
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #ddd;
            background-color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .pagination button:hover:not(:disabled) {
            background-color: #e9e9e9;
        }
        .pagination button.active {
            background-color: #2c3e50;
            color: white;
            border-color: #2c3e50;
        }
        .pagination button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        #loadingIndicator, #messagesLoadingIndicator { /* Added messagesLoadingIndicator */
            text-align: center;
            margin: 20px 0;
            display: none;
            color: #555;
        }
        /* 过滤器样式 */
        .filters {
            display: flex;
            justify-content: center;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
            margin-bottom: 20px;
            gap: 10px;
        }
        .filters select, .filters input {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .info-text {
            text-align: center;
            margin: 20px;
            color: #777;
        }
    </style>
</head>
<body>
    <h1>用户对话历史</h1>

    <div class="filters">
        <input type="text" id="searchInput" placeholder="搜索用户ID...">
        <select id="dateFilter">
            <option value="all">所有时间</option>
            <option value="today">今天</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
        </select>
        <select id="messageCountFilter">
            <option value="all">所有对话长度</option>
            <option value="short">短对话 (≤10条消息)</option>
            <option value="medium">中等对话 (11-40条)</option>
            <option value="long">长对话 (>40条)</option>
        </select>
        <button id="applyFilterButton" style="padding: 8px 15px; background-color: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">应用过滤</button>
    </div>

    <div id="loadingIndicator">加载会话列表中...</div>
    <div id="chatContainer"></div>
    <div class="pagination" id="pagination"></div>
    <div id="messagesLoadingIndicator" style="display: none;">加载消息中...</div>

    <template id="userSectionTemplate">
        <div class="user-section" data-user-id="" data-session-id="">
            <div class="user-header">
                <div>用户ID: <strong class="user-id-value"></strong> | 对话轮数: <strong class="message-interaction-count"></strong> (共 <span class="total-raw-message-count"></span> 条)</div>
                <div class="timestamp">最后更新: <span class="last-update-time"></span></div>
                <span class="toggle-btn-indicator">[+]</span> </div>
            <div class="conversation">
                </div>
        </div>
    </template>

    <template id="messageTemplate">
        <div class="message">
            <div class="message-header">
                <span class="message-type"></span>
                <span class="timestamp"></span>
            </div>
            <div class="message-content"></div>
        </div>
    </template>

    <script>
        const PAGE_SIZE = 5; //每页显示的会话摘要数量
        let currentPage = 1;
        let totalItems = 0;
        let totalPages = 0;

        // DOM Elements
        const searchInput = document.getElementById('searchInput');
        const dateFilterSelect = document.getElementById('dateFilter');
        const messageCountFilterSelect = document.getElementById('messageCountFilter');
        const applyFilterButton = document.getElementById('applyFilterButton');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const messagesLoadingIndicator = document.getElementById('messagesLoadingIndicator');
        const chatContainer = document.getElementById('chatContainer');
        const paginationElement = document.getElementById('pagination');

        window.onload = function() {
            fetchChatSessions();
            applyFilterButton.addEventListener('click', () => {
                currentPage = 1; // Reset to first page on new filter application
                fetchChatSessions();
            });
            // Optional: trigger search on Enter key
            searchInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault(); // Prevent form submission if it's in a form
                    applyFilterButton.click();
                }
            });
        };

        async function fetchChatSessions() {
            loadingIndicator.style.display = 'block';
            chatContainer.innerHTML = ''; // Clear previous results
            paginationElement.innerHTML = ''; // Clear pagination

            const searchText = searchInput.value;
            const dateFilter = dateFilterSelect.value;
            const messageCountFilter = messageCountFilterSelect.value;

            // Adjust API path if your FastAPI app is not at the root
            // Example: const currentPath = window.location.pathname.replace(/\/chat_history\/?$/, '');
            // const dataUrl = `${currentPath}/api/data?page=${currentPage}&page_size=${PAGE_SIZE}&...`;
            const dataUrl = `/chat_history/api/data?page=${currentPage}&page_size=${PAGE_SIZE}` +
                            `&search_text=${encodeURIComponent(searchText)}` +
                            `&date_filter=${dateFilter}` +
                            `&message_count_filter=${messageCountFilter}`;

            try {
                const response = await fetch(dataUrl);
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: '未知错误' }));
                    throw new Error(`获取会话失败: ${response.status} ${errorData.detail || ''}`);
                }
                const data = await response.json();
                allChatSessionSummaries = data.items; // Renamed for clarity
                totalItems = data.total;
                totalPages = data.pages;

                renderChatSummaries(allChatSessionSummaries);
                renderPagination();

                if (totalItems === 0) {
                    chatContainer.innerHTML = '<div class="info-text">没有找到匹配的对话记录。</div>';
                }

            } catch (error) {
                console.error('获取会话数据错误:', error);
                chatContainer.innerHTML = `<div class="info-text" style="color: red;">加载聊天数据时出错: ${error.message}</div>`;
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        function renderChatSummaries(summaries) {
            chatContainer.innerHTML = ''; // Clear previous items
            const userSectionTemplate = document.getElementById('userSectionTemplate');

            summaries.forEach(summary => {
                const newUserSection = document.importNode(userSectionTemplate.content, true).firstElementChild;
                newUserSection.setAttribute('data-user-id', summary.user_id);
                newUserSection.setAttribute('data-session-id', summary.session_id); // Store session_id

                newUserSection.querySelector('.user-id-value').textContent = summary.user_id;
                newUserSection.querySelector('.message-interaction-count').textContent = summary.message_interaction_count;
                newUserSection.querySelector('.total-raw-message-count').textContent = summary.total_raw_message_count;
                newUserSection.querySelector('.last-update-time').textContent = summary.last_update ? formatDate(new Date(summary.last_update)) : 'N/A';

                // Add click listener to header for toggling
                const header = newUserSection.querySelector('.user-header');
                header.onclick = function() { toggleConversation(newUserSection); };

                chatContainer.appendChild(newUserSection);
            });
        }

        function renderPagination() {
            paginationElement.innerHTML = '';
            if (totalPages <= 1) return;

            let paginationHTML = '';
            paginationHTML += `<button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;

            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
            if (endPage - startPage + 1 < maxVisiblePages && totalPages >= maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                paginationHTML += `<button onclick="changePage(1)">1</button>`;
                if (startPage > 2) paginationHTML += `<span>...</span>`;
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button onclick="changePage(${i})" class="${i === currentPage ? 'active' : ''}">${i}</button>`;
            }

             if (endPage < totalPages) {
                if (endPage < totalPages - 1) paginationHTML += `<span>...</span>`;
                paginationHTML += `<button onclick="changePage(${totalPages})">${totalPages}</button>`;
            }

            paginationHTML += `<button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
            paginationElement.innerHTML = paginationHTML;
        }

        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            currentPage = page;
            fetchChatSessions(); // Fetch data for the new page
            window.scrollTo(0, 0);
        }

        function formatDate(date) {
            if (!date || isNaN(date)) return '无效日期';
            return date.toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit',
                hour12: false
            });
        }

        async function toggleConversation(userSectionElement) {
            const conversationDiv = userSectionElement.querySelector('.conversation');
            const sessionId = userSectionElement.getAttribute('data-session-id');
            const indicator = userSectionElement.querySelector('.toggle-btn-indicator');

            if (conversationDiv.style.display === 'none' || conversationDiv.style.display === '') {
                // If messages are not loaded yet or need refresh
                if (conversationDiv.innerHTML.trim() === '' || userSectionElement.dataset.needsRefresh === 'true') {
                    await loadAndRenderMessages(sessionId, conversationDiv);
                    userSectionElement.dataset.needsRefresh = 'false'; // Reset refresh flag
                }
                conversationDiv.style.display = 'block';
                indicator.textContent = '[-]';
            } else {
                conversationDiv.style.display = 'none';
                indicator.textContent = '[+]';
            }
        }

        async function loadAndRenderMessages(sessionId, conversationDiv) {
            conversationDiv.innerHTML = '<p style="text-align:center; color:#777;">加载消息中...</p>'; // Temporary loading message
            messagesLoadingIndicator.style.display = 'block'; // Global messages loading indicator (optional)

            try {
                const response = await fetch(`/chat_history/api/sessions/${sessionId}/messages`);
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: '加载消息失败' }));
                    throw new Error(`Failed to fetch messages: ${response.status} ${errorData.detail || ''}`);
                }
                const messages = await response.json();
                conversationDiv.innerHTML = ''; // Clear loading message

                if (!messages || messages.length === 0) {
                    conversationDiv.innerHTML = '<p style="text-align:center; color:#777;">此会话没有消息。</p>';
                    return;
                }

                const messageTemplate = document.getElementById('messageTemplate');
                const fragment = document.createDocumentFragment();

                messages.forEach((message, index) => {
                    const newMessageElement = document.importNode(messageTemplate.content, true).firstElementChild;
                    newMessageElement.classList.add(message.type); // human, ai, error, unknown

                    let messageTypeText = '未知';
                    if (message.type === 'human') messageTypeText = '用户';
                    else if (message.type === 'ai') messageTypeText = 'AI';
                    else if (message.type === 'error') messageTypeText = '错误';

                    newMessageElement.querySelector('.message-type').textContent = messageTypeText;
                    newMessageElement.querySelector('.timestamp').textContent = message.timestamp ? formatDate(new Date(message.timestamp)) : 'N/A';

                    const contentElement = newMessageElement.querySelector('.message-content');
                    contentElement.textContent = message.content;

                    if ((message.type === 'ai' || message.type === 'human') && message.content && message.content.length > 250) { // Apply to human too if desired
                        contentElement.classList.add('message-truncated');
                        contentElement.id = `content-${sessionId}-${index}`; // Unique ID

                        const expandButton = document.createElement('button');
                        expandButton.className = 'expand-btn';
                        expandButton.textContent = '展开/收起';
                        expandButton.onclick = function(event) {
                            event.stopPropagation(); // Prevent collapsing the whole conversation
                            toggleContentTruncation(contentElement.id);
                        };
                        newMessageElement.appendChild(expandButton);
                    }
                    fragment.appendChild(newMessageElement);
                });
                conversationDiv.appendChild(fragment);

            } catch (error) {
                console.error(`Error loading messages for session ${sessionId}:`, error);
                conversationDiv.innerHTML = `<p style="text-align:center; color:red;">加载消息失败: ${error.message}</p>`;
            } finally {
                messagesLoadingIndicator.style.display = 'none';
            }
        }

        function toggleContentTruncation(contentId) { // Renamed for clarity
            const contentDiv = document.getElementById(contentId);
            if (contentDiv) {
                contentDiv.classList.toggle('message-truncated');
            }
        }

    </script>
</body>
</html>
