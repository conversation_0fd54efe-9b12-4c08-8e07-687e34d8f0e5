<!-- templates/knowledge_mange.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件处理系统[后台]</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"], textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            min-height: 400px;
            font-family: monospace;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .alert-danger {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        .col {
            flex: 1;
            padding: 0 10px;
            box-sizing: border-box;
        }
        .spinner {
            display: inline-block;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .chunk-item {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .chunk-item pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
        .chunk-count {
            font-weight: bold;
            margin-bottom: 10px;
            color: #3c763d;
        }
        .chunks-container {
            max-height: 600px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .select-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .refresh-button {
            background-color: #007bff;
            flex-shrink: 0;
        }
        .refresh-button:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件处理系统</h1>

        <div id="alertContainer"></div>

        <div class="form-group">
            <label for="fileInput">选择文件:</label>
            <input type="file" id="fileInput">
            <button id="uploadBtn" onclick="uploadFile()">上传并解析文件</button>
        </div>

        <div class="row" style="margin-top: 20px;">
            <div class="col">
                <div class="form-group">
                    <label for="markdownEditor">文档内容 (可编辑):</label>
                    <textarea id="markdownEditor" placeholder="上传文件后将在此显示解析结果..." disabled></textarea>
                </div>
            </div>

            <div class="col">
                <div class="form-group">
                    <label for="chunksPreview">文本块预览:</label>
                    <button id="splitBtn" onclick="previewChunks()" disabled>切割预览</button>
                    <div id="chunksPreview" class="chunks-container">
                        <div class="chunk-count" id="chunkCount"></div>
                        <div id="chunksContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="categoryInput">文件类别:</label>
            <input type="text" id="categoryInput" placeholder="请输入文件类别 (仅限英文字符)"
                   oninput="this.value = this.value.replace(/[^a-zA-Z]/g, '')">
        </div>

        <!-- 新增集合选择 -->
        <div class="form-group">
            <label for="collectionSelect">选择目标集合:</label>
            <div class="select-container">
                <select id="collectionSelect">
                    <option value="knowledge_20250303">knowledge_20250303</option>
                </select>
                <button class="refresh-button" onclick="loadCollections()">刷新集合列表</button>
            </div>
        </div>

        <button id="storeBtn" onclick="storeToVector()" disabled>转为向量数据入库</button>
    </div>

    <script>
        // 存储文件信息
        let currentFileInfo = null;
        let currentChunks = [];

        // 页面加载时获取集合列表
        document.addEventListener('DOMContentLoaded', function() {
            loadCollections();
        });

        // 加载集合列表
        async function loadCollections() {
            try {
                const response = await fetch('/knowledge_mange/list-collections');
                const result = await response.json();

                if (result.success) {
                    const collectionSelect = document.getElementById('collectionSelect');
                    collectionSelect.innerHTML = '';

                    // 添加所有集合到下拉列表
                    result.data.forEach(collection => {
                        const option = document.createElement('option');
                        option.value = collection;
                        option.textContent = collection;
                        collectionSelect.appendChild(option);
                    });

                    // 默认选择第一个集合
                    if (result.data.length > 0) {
                        collectionSelect.value = result.data[0];
                    }

                    showAlert('集合列表已更新', 'success');
                } else {
                    showAlert(`获取集合列表失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`获取集合列表出错: ${error.message}`, 'error');
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type === 'success' ? 'alert-success' : 'alert-danger'}`;
            alertDiv.textContent = message;

            // 清除之前的提示
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);

            // 5秒后自动消失
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // 上传并解析文件
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                showAlert('请选择要上传的文件', 'error');
                return;
            }

            // 禁用按钮并显示加载状态
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '处理中...<span class="spinner"></span>';

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/knowledge_mange/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 显示解析结果
                    const markdownEditor = document.getElementById('markdownEditor');
                    markdownEditor.value = result.markdown_text;
                    markdownEditor.disabled = false;

                    // 存储文件信息，添加原始文件名
                    currentFileInfo = {
                        fileId: result.file_id,
                        filePath: result.file_path,
                        originalFileName: file.name  // 保存原始文件名
                    };

                    // 启用切割按钮
                    document.getElementById('splitBtn').disabled = false;

                    // 存储按钮暂时保持禁用状态，直到用户预览切割结果
                    document.getElementById('storeBtn').disabled = true;

                    showAlert('文件上传并解析成功，可以编辑内容后点击切割预览', 'success');
                } else {
                    showAlert(`文件处理失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`上传出错: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                uploadBtn.disabled = false;
                uploadBtn.innerHTML = '上传并解析文件';
            }
        }

        // 预览文本块
        async function previewChunks() {
            const markdownText = document.getElementById('markdownEditor').value;
            if (!markdownText) {
                showAlert('文档内容不能为空', 'error');
                return;
            }

            // 禁用按钮并显示加载状态
            const splitBtn = document.getElementById('splitBtn');
            splitBtn.disabled = true;
            splitBtn.innerHTML = '处理中...<span class="spinner"></span>';

            const formData = new FormData();
            formData.append('text', markdownText);
            formData.append('source', currentFileInfo ? currentFileInfo.filePath : '');

            try {
                const response = await fetch('/knowledge_mange/preview-chunks', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 存储切割结果
                    currentChunks = result.chunks;

                    // 显示切割结果
                    displayChunks(currentChunks);

                    // 更新块数量
                    document.getElementById('chunkCount').textContent = `共 ${currentChunks.length} 个文本块`;

                    // 启用存储按钮
                    document.getElementById('storeBtn').disabled = false;

                    showAlert(`文本已成功切割为 ${currentChunks.length} 个块`, 'success');
                } else {
                    showAlert(`文本切割失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`处理出错: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                splitBtn.disabled = false;
                splitBtn.innerHTML = '切割预览';
            }
        }

        // 显示文本块
        function displayChunks(chunks) {
            const chunksContainer = document.getElementById('chunksContainer');
            chunksContainer.innerHTML = '';

            chunks.forEach((chunk, index) => {
                const chunkDiv = document.createElement('div');
                chunkDiv.className = 'chunk-item';

                const chunkHeader = document.createElement('div');
                chunkHeader.innerHTML = `<strong>块 #${index + 1}</strong>`;

                const chunkContent = document.createElement('pre');
                chunkContent.textContent = chunk.content;

                chunkDiv.appendChild(chunkHeader);
                chunkDiv.appendChild(chunkContent);
                chunksContainer.appendChild(chunkDiv);
            });
        }

        // 存入向量数据库
        async function storeToVector() {
            if (!currentFileInfo) {
                showAlert('请先上传并解析文件', 'error');
                return;
            }

            if (currentChunks.length === 0) {
                showAlert('请先切割文本', 'error');
                return;
            }

            const markdownText = document.getElementById('markdownEditor').value;
            if (!markdownText) {
                showAlert('文档内容不能为空', 'error');
                return;
            }

            const category = document.getElementById('categoryInput').value;
            const collectionName = document.getElementById('collectionSelect').value;

            // 禁用按钮并显示加载状态
            const storeBtn = document.getElementById('storeBtn');
            storeBtn.disabled = true;
            storeBtn.innerHTML = '处理中...<span class="spinner"></span>';

            // 修改 storeToVector() 函数中的代码
            const formData = new FormData();
            formData.append('markdown_text', markdownText);
            formData.append('category', category);
            formData.append('source', currentFileInfo.filePath);  // 传递文件路径作为后端识别用
            formData.append('original_filename', currentFileInfo.originalFileName);  // 传递原始文件名
            formData.append('collection_name', collectionName);  // 传递集合名称

            try {
                const response = await fetch('/knowledge_mange/store-vector', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 优化成功提示信息，显示文件名和类别信息
                    const fileName = currentFileInfo.originalFileName || '文件';
                    const categoryInfo = category ? `，类别：${category}` : '';
                    const collectionInfo = `，集合：${collectionName}`;
                    showAlert(`${fileName}${categoryInfo}${collectionInfo} 成功转换为向量数据并入库！`, 'success');

                    // 重置界面状态，便于进行下一次操作
                    resetFormAfterSuccess();
                } else {
                    showAlert(`向量存储失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showAlert(`处理出错: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                storeBtn.disabled = false;
                storeBtn.innerHTML = '转为向量数据入库';
            }
        }

        // 成功入库后重置表单状态
        function resetFormAfterSuccess() {
            // 清空输入字段
            document.getElementById('fileInput').value = '';
            document.getElementById('markdownEditor').value = '';
            document.getElementById('markdownEditor').disabled = true;
            document.getElementById('categoryInput').value = '';

            // 重置按钮状态
            document.getElementById('splitBtn').disabled = true;
            document.getElementById('storeBtn').disabled = true;

            // 清空文本块预览
            document.getElementById('chunksContainer').innerHTML = '';
            document.getElementById('chunkCount').textContent = '';

            // 重置文件信息
            currentFileInfo = null;
            currentChunks = [];
        }
    </script>
</body>
</html>