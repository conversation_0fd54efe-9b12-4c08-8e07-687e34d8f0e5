aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings @ file:///croot/argon2-cffi-bindings_1736182440035/work
arrow==1.3.0
asgiref==3.8.1
asttokens==3.0.0
async-lru @ file:///croot/async-lru_1699554519285/work
attrs==25.1.0
babel==2.17.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.3
bleach @ file:///croot/bleach_1732290411627/work
blinker==1.9.0
boto3==1.37.4
botocore==1.37.4
Brotli==1.1.0
build==1.2.2.post1
cachetools==5.5.2
certifi @ file:///croot/certifi_1738623731865/work/certifi
cffi @ file:///croot/cffi_1736182485317/work
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.6.3
click==8.1.8
coloredlogs==15.0.1
colorlog==6.9.0
comm==0.2.2
cryptography==44.0.2
dataclasses-json==0.6.7
db-dtypes==1.4.1
debugpy==1.8.12
decorator==5.2.1
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
Deprecated==1.2.18
dill==0.3.9
distro==1.9.0
docling==2.25.0
docling-core==2.21.1
docling-ibm-models==3.4.1
docling-parse==3.4.0
durationpy==0.9
easyocr==1.7.2
et_xmlfile==2.0.0
executing==2.2.0
fast-langdetect==0.2.5
fastapi==0.115.10
fastjsonschema==2.21.1
fasttext-predict==*******
filelock==3.17.0
filetype==1.2.0
flasgger==*******
Flask==3.1.0
flask-sock==0.7.0
flatbuffers==25.2.10
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2025.2.0
google-auth==2.38.0
googleapis-common-protos==1.69.0
greenlet==3.1.1
grpcio==1.67.1
gunicorn==23.0.0
h11 @ file:///croot/h11_1706652277403/work
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.1
humanfriendly==10.0
idna==3.10
imageio==2.37.0
importlib_metadata==8.5.0
importlib_resources==6.5.2
ipykernel @ file:///croot/ipykernel_1737660677549/work
ipython==9.0.1
ipython_pygments_lexers==1.1.1
ipywidgets @ file:///croot/ipywidgets_1733504575802/work
isoduration==20.11.0
itsdangerous==2.2.0
jedi @ file:///croot/jedi_1733987392413/work
Jinja2 @ file:///croot/jinja2_1737760107953/work
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
json5==0.10.0
jsonlines==3.1.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema @ file:///croot/jsonschema_1728486696720/work
jsonschema-specifications==2024.10.1
jupyter @ file:///croot/jupyter_1737645803214/work
jupyter-console @ file:///croot/jupyter_console_1679999630278/work
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client @ file:///croot/jupyter_client_1737570961872/work
jupyter_core @ file:///croot/jupyter_core_1718818295206/work
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.5
jupyterlab_pygments @ file:///croot/jupyterlab_pygments_1741124142640/work
jupyterlab_server @ file:///croot/jupyterlab_server_1725865349919/work
jupyterlab_widgets @ file:///croot/jupyterlab_widgets_1733440870459/work
kaleido==0.2.1
kubernetes==32.0.1
langchain==0.3.19
langchain-community==0.3.18
langchain-core==0.3.40
langchain-openai==0.3.7
langchain-postgres==0.0.13
langchain-text-splitters==0.3.6
langsmith==0.3.11
latex2mathml==3.77.0
lazy_loader==0.4
loguru==0.7.3
lxml==5.3.1
magic-pdf==0.6.1
markdown-it-py==3.0.0
marko==2.1.2
MarkupSafe @ file:///croot/markupsafe_1738584038848/work
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
milvus-lite==2.4.11
mistune @ file:///croot/mistune_1741124011532/work
mmh3==5.1.0
monotonic==1.6
mpire==2.10.2
mpmath==1.3.0
multidict==6.1.0
multiprocess==0.70.17
mypy-extensions==1.0.0
narwhals==1.29.0
nbclient @ file:///croot/nbclient_1741123995822/work
nbconvert==7.16.6
nbformat @ file:///croot/nbformat_1728049424075/work
nest-asyncio @ file:///croot/nest-asyncio_1708532673751/work
networkx==3.4.2
ninja==********
notebook @ file:///croot/notebook_1738159946465/work
notebook_shim==0.2.4
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
oauthlib==3.2.2
onnxruntime==1.16.3
openai==1.65.1
opencv-python-headless==*********
openpyxl==3.1.5
opentelemetry-api==1.30.0
opentelemetry-exporter-otlp-proto-common==1.30.0
opentelemetry-exporter-otlp-proto-grpc==1.30.0
opentelemetry-instrumentation==0.51b0
opentelemetry-instrumentation-asgi==0.51b0
opentelemetry-instrumentation-fastapi==0.51b0
opentelemetry-proto==1.30.0
opentelemetry-sdk==1.30.0
opentelemetry-semantic-conventions==0.51b0
opentelemetry-util-http==0.51b0
orjson==3.10.15
overrides==7.7.0
packaging @ file:///croot/packaging_1734472117206/work
pandas==2.2.3
pandocfilters==1.5.1
parso @ file:///croot/parso_1733963305961/work
pdfminer.six==20240706
pexpect==4.9.0
pgvector==0.3.6
pillow==11.1.0
platformdirs==4.3.6
plotly==6.0.0
posthog==3.18.1
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==5.29.3
psutil==7.0.0
psycopg==3.2.5
psycopg-pool==3.2.6
psycopg2==2.9.10
psycopg2-binary==2.9.10
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure_eval==0.2.3
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
Pygments==2.19.1
pymilvus==2.5.4
PyMuPDF==1.25.3
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
PyQt6==6.7.1
PyQt6_sip @ file:///croot/pyqt-split_1740498191142/work/pyqt_sip
PySocks @ file:///work/ci_py311/pysocks_1676822712504/work
python-bidi==0.6.6
python-dateutil @ file:///croot/python-dateutil_1716495738603/work
python-docx==1.1.2
python-dotenv==1.0.1
python-json-logger @ file:///croot/python-json-logger_1734370021104/work
python-pptx==1.0.2
pytz==2025.1
PyYAML @ file:///croot/pyyaml_1728657952215/work
pyzmq==26.2.1
qtconsole @ file:///croot/qtconsole_1737590761179/work
QtPy @ file:///croot/qtpy_1700144840038/work
referencing==0.36.2
regex==2024.11.6
requests @ file:///croot/requests_1730999120400/work
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3339-validator @ file:///croot/rfc3339-validator_1683077044675/work
rfc3986-validator @ file:///croot/rfc3986-validator_1683058983515/work
rich==13.9.4
robust-downloader==0.0.2
rpds-py==0.23.1
rsa==4.9
Rtree==1.3.0
s3transfer==0.11.3
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.2
semchunk==2.2.2
Send2Trash==1.8.3
shapely==2.0.7
shellingham==1.5.4
simple-websocket==1.1.0
sip @ file:///croot/sip_1738856193618/work
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.38
sqlparse==0.5.3
stack-data==0.6.3
starlette==0.46.0
sympy==1.13.1
tabulate==0.9.0
tenacity==9.0.0
terminado==0.18.1
threadpoolctl==3.5.0
tifffile==2025.2.18
tiktoken==0.9.0
tinycss2 @ file:///croot/tinycss2_1738337643607/work
tokenizers==0.21.0
torch==2.6.0
torchvision==0.21.0
tornado @ file:///croot/tornado_1733960490606/work
tqdm==4.67.1
traitlets @ file:///croot/traitlets_1718227057033/work
transformers==4.49.0
triton==3.2.0
typer==0.12.5
types-python-dateutil==2.9.0.20241206
typing-inspect==0.9.0
typing_extensions @ file:///croot/typing_extensions_1734714854207/work
tzdata==2025.1
ujson==5.10.0
uri-template==1.3.0
urllib3 @ file:///croot/urllib3_1737133630106/work
uvicorn==0.34.0
uvloop==0.21.0
vanna==0.7.6
watchfiles==1.0.4
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client @ file:///croot/websocket-client_1715878298792/work
websockets==15.0
Werkzeug==3.1.3
widgetsnbextension @ file:///croot/widgetsnbextension_1733439572884/work
wordninja==2.0.0
wrapt==1.17.2
wsproto==1.2.0
XlsxWriter==3.2.2
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
